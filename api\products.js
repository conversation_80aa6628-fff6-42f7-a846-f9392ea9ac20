import { Pool } from 'pg';

// Configurazione database PostgreSQL
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

export default async function handler(req, res) {
  // Abilita CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Debug connessione database
    console.log('🔍 DATABASE_URL presente:', !!process.env.DATABASE_URL);
    console.log('🔍 DATABASE_URL lunghezza:', process.env.DATABASE_URL?.length || 0);
    console.log('🔍 DATABASE_URL inizio:', process.env.DATABASE_URL?.substring(0, 20) || 'N/A');
    if (req.method === 'GET') {
      // Ottieni tutti i prodotti
      const result = await pool.query(
        'SELECT * FROM products WHERE available = true ORDER BY category, name'
      );
      
      res.status(200).json(result.rows);
    } else {
      res.status(405).json({ error: 'Metodo non supportato' });
    }
  } catch (error) {
    console.error('Errore API prodotti:', error);
    res.status(500).json({ 
      error: 'Errore interno del server',
      details: error.message 
    });
  }
}
