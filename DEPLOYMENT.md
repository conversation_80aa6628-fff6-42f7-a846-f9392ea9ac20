# 🚀 Guida al Deployment - Pizzeria Jasmin

## Preparazione per la Produzione

### 1. Problemi Risolti
- ✅ Corretti errori nel database (riferimenti `db` vs `this.db`)
- ✅ Rimossi URL hardcoded e implementata configurazione dinamica
- ✅ Corretti warning ESLint (variabili non utilizzate)
- ✅ Implementato sistema di configurazione centralizzato

### 2. Configurazione dell'Ambiente

#### Frontend
1. Copia il file `.env.example` in `.env`:
   ```bash
   cp .env.example .env
   ```

2. Modifica le variabili per la produzione:
   ```env
   REACT_APP_API_URL=https://tuodominio.com
   ```

#### Backend
1. Configura la porta del server:
   ```env
   PORT=3000
   ```

2. Configura il percorso del database:
   ```env
   DATABASE_PATH=./pizzeria.db
   ```

### 3. Build per la Produzione

#### Frontend
```bash
npm run build
```

#### Backend
Il backend è già pronto per la produzione. Assicurati che:
- Il database SQLite sia accessibile
- Le dipendenze siano installate
- Il server Express sia configurato correttamente

### 4. Deployment

#### Opzione 1: Server Tradizionale
1. Carica i file sul server
2. Installa Node.js e npm
3. Installa le dipendenze:
   ```bash
   npm install --production
   ```
4. Avvia il server:
   ```bash
   node server.js
   ```

#### Opzione 2: Docker (Raccomandato)
Crea un `Dockerfile`:
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["node", "server.js"]
```

#### Opzione 3: Servizi Cloud
- **Vercel**: Perfetto per il frontend React
- **Heroku**: Buono per applicazioni full-stack
- **DigitalOcean**: Server VPS economico
- **AWS/Azure**: Per deployment enterprise

### 5. Configurazione Reverse Proxy (Nginx)

```nginx
server {
    listen 80;
    server_name tuodominio.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 6. SSL/HTTPS
Usa Let's Encrypt per certificati SSL gratuiti:
```bash
sudo certbot --nginx -d tuodominio.com
```

### 7. Monitoraggio e Backup

#### Database Backup
```bash
# Backup automatico del database
cp pizzeria.db backup/pizzeria_$(date +%Y%m%d_%H%M%S).db
```

#### Log Monitoring
Implementa logging per monitorare:
- Errori del server
- Ordini ricevuti
- Performance delle API

### 8. Checklist Pre-Deployment

- [ ] Test completo dell'applicazione
- [ ] Configurazione delle variabili d'ambiente
- [ ] Build di produzione testato
- [ ] Database inizializzato
- [ ] SSL configurato
- [ ] Backup strategy implementata
- [ ] Monitoring configurato
- [ ] Test di carico eseguiti

### 9. Post-Deployment

1. **Test dell'applicazione live**
2. **Verifica funzionalità Stripe** (se abilitata)
3. **Test ordini end-to-end**
4. **Monitoraggio performance**
5. **Setup backup automatici**

### 10. Manutenzione

#### Aggiornamenti
- Testa sempre in ambiente di staging
- Fai backup prima degli aggiornamenti
- Monitora dopo il deployment

#### Sicurezza
- Aggiorna regolarmente le dipendenze
- Monitora vulnerabilità con `npm audit`
- Implementa rate limiting se necessario

## Supporto

Per problemi di deployment, controlla:
1. Log del server
2. Console del browser per errori frontend
3. Connettività database
4. Configurazione delle variabili d'ambiente
