{"version": 3, "file": "static/css/main.cf253cbf.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CAGA,oBACE,SACF,CAEA,0BACE,kBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CC7BA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAIE,wBAAyB,CADzB,UAAW,CAFX,6BAAiC,CACjC,eAGF,CAEA,KACE,gBACF,CAGA,MACE,uBAAwB,CACxB,sBAAuB,CACvB,yBAA0B,CAC1B,uBAAwB,CACxB,uBAAwB,CACxB,iBAAkB,CAClB,iBAAkB,CAClB,0BAA2B,CAC3B,YAAgB,CAChB,6BAAoC,CACpC,mBACF,CAGA,QACE,kDAA8E,CAA9E,2EAA8E,CAG9E,+BAAyB,CAAzB,wBAAyB,CAFzB,UAAY,CACZ,cAAe,CAEf,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,WACF,CAEA,gBAKE,6BAA8B,CAH9B,aAAc,CADd,gBAAiB,CAEjB,cAIF,CAEA,sBAHE,kBAAmB,CAFnB,YAWF,CANA,MAIE,gBAAiB,CACjB,eAAiB,CAFjB,SAGF,CAEA,KAGE,kBAAmB,CAFnB,YAAa,CACb,QAEF,CAEA,OAIE,iBAAmC,CAAnC,kCAAmC,CAHnC,UAAY,CAEZ,kBAAoB,CADpB,oBAAqB,CAGrB,+BACF,CAEA,aACE,0BACF,CAEA,WAEE,kBAAkC,CAAlC,iCAAkC,CAElC,iBAAkB,CAClB,cAAe,CAFf,aAAe,CAFf,iBAAkB,CAKlB,wBACF,CAEA,iBACE,oBACF,CAEA,YAUE,kBAAmB,CANnB,kBAAgC,CAAhC,+BAAgC,CAEhC,iBAAkB,CADlB,UAAY,CAIZ,YAAa,CAGb,eAAiB,CACjB,eAAiB,CALjB,WAAY,CAGZ,sBAAuB,CAVvB,iBAAkB,CAElB,UAAW,CADX,QAAS,CAKT,UAOF,CAGA,WAEE,aAAc,CADd,gBAAiB,CAEjB,iBACF,CAGA,KAEE,WAAY,CACZ,iBAAmC,CAAnC,kCAAmC,CACnC,cAAe,CAKf,oBAAqB,CAJrB,cAAe,CACf,eAAgB,CALhB,qBAAuB,CASvB,iBAAkB,CAFlB,oBAAqB,CADrB,kBAIF,CAEA,aACE,kBAAgC,CAAhC,+BAAgC,CAChC,UACF,CAEA,mBACE,kBAA+B,CAA/B,8BAA+B,CAC/B,0BACF,CAEA,eACE,kBAAkC,CAAlC,iCAAkC,CAClC,UACF,CAEA,qBACE,kBAAmB,CACnB,0BACF,CAEA,aACE,kBAAgC,CAAhC,+BAAgC,CAChC,UACF,CAEA,mBACE,kBACF,CAEA,aACE,gBAAuB,CACvB,wBAAsC,CAAtC,qCAAsC,CACtC,aAA2B,CAA3B,0BACF,CAEA,mBACE,kBAAgC,CAAhC,+BAAgC,CAChC,UACF,CAGA,MACE,eAAiB,CACjB,iBAAmC,CAAnC,kCAAmC,CACnC,+BAAyB,CAAzB,wBAAyB,CACzB,eAAgB,CAChB,wBACF,CAEA,UACE,6BACF,CAEA,gBACE,qBACF,CAEA,YACE,0BACF,CAEA,aAEE,kBAAmC,CAAnC,kCAAmC,CACnC,4BAA6B,CAF7B,YAGF,CAEA,WACE,cACF,CAEA,aAEE,kBAAmC,CAAnC,kCAAmC,CACnC,yBAA0B,CAF1B,YAGF,CAGA,MAEE,eAAW,CADX,YAAa,CACb,UACF,CAEA,QACE,wDACF,CAEA,QACE,wDACF,CAEA,QACE,wDACF,CAGA,YACE,kBACF,CAEA,YAIE,UAAwB,CAAxB,uBAAwB,CAHxB,aAAc,CAEd,eAAgB,CADhB,mBAGF,CAEA,YAGE,qBAAsB,CACtB,iBAAmC,CAAnC,kCAAmC,CACnC,cAAe,CAHf,cAAgB,CAIhB,2BAA6B,CAL7B,UAMF,CAEA,kBAEE,oBAAkC,CAAlC,iCAAkC,CADlC,YAEF,CAEA,aAME,eAAiB,CAHjB,qBAAsB,CACtB,iBAAmC,CAAnC,kCAAmC,CAGnC,cAAe,CAFf,cAAe,CAHf,cAAgB,CADhB,UAOF,CAGA,aAAe,iBAAoB,CACnC,WAAa,eAAkB,CAC/B,YAAc,gBAAmB,CAEjC,MAAQ,gBAAoB,CAC5B,MAAQ,eAAkB,CAC1B,MAAQ,iBAAoB,CAC5B,MAAQ,mBAAuB,CAC/B,MAAQ,kBAAqB,CAC7B,MAAQ,oBAAuB,CAE/B,KAAO,aAAiB,CACxB,KAAO,YAAe,CACtB,KAAO,cAAiB,CAExB,MAAQ,YAAe,CACvB,aAAmD,sBAAyB,CAC5E,2BAD8B,kBAAmB,CAAlC,YACqE,CAApF,cAAoD,6BAAgC,CACpF,aAAe,qBAAwB,CACvC,OAAS,SAAa,CACtB,OAAS,QAAW,CACpB,OAAS,UAAa,CAEtB,QAAU,UAAa,CACvB,QAAU,WAAc,CAGxB,cAEE,kBAAmB,CACnB,eAAiB,CACjB,eAAiB,CAHjB,qBAAwB,CAIxB,wBACF,CAEA,iBACE,kBAAmB,CACnB,aACF,CAEA,qBACE,kBAAmB,CACnB,aACF,CAEA,eACE,kBAAmB,CACnB,aACF,CAEA,mBACE,kBAAmB,CACnB,aACF,CAGA,kBACE,GAAO,SAAU,CAAE,0BAA6B,CAChD,GAAK,SAAU,CAAE,uBAA0B,CAC7C,CAEA,iBACE,MAAW,kBAAqB,CAChC,IAAM,qBAAwB,CAChC,CAEA,SACE,6BACF,CAEA,OACE,2BACF,CAGA,YAEE,oBAAqB,CADrB,iBAEF,CAEA,0DASE,eAAiB,CAJjB,wBAAyB,CACzB,iBAAmC,CAAnC,kCAAmC,CACnC,cAAe,CAHf,YAAa,CAIb,uBAAyB,CALzB,UAOF,CAEA,4EAIE,oBAAkC,CAAlC,iCAAkC,CAClC,8BAA4C,CAF5C,YAGF,CAEA,4EAIE,wBAAyB,CADzB,oBAAkC,CAAlC,iCAEF,CAEA,kFAIE,wBAAyB,CADzB,oBAAkC,CAAlC,iCAEF,CAEA,eAKE,kBAAmB,CAEnB,yBAA2B,CAN3B,aAA2B,CAA3B,0BAA2B,CAG3B,YAAa,CAFb,iBAAmB,CAInB,SAAW,CAHX,gBAKF,CAEA,YACE,aAA2B,CAA3B,0BAMF,CAEA,0BAHE,mBAAoB,CAJpB,iBAAkB,CAClB,UAAW,CACX,OAAQ,CACR,0BAWF,CAPA,cACE,aAA2B,CAA3B,0BAMF,CAGA,aAEE,WAAkB,CADlB,iBAEF,CAEA,mBAYE,iCAAkC,CAHlC,sBAA6B,CAE7B,iBAAkB,CADlB,wBAA8B,CAT9B,UAAW,CAGX,WAAY,CAEZ,QAAS,CACT,iBAAkB,CAClB,gBAAiB,CANjB,iBAAkB,CAGlB,OAAQ,CAFR,UAUF,CAOA,MACE,qBACF,CAGA,yBACE,WACE,YACF,CAEA,gBACE,cACF,CAMA,aAHE,QAMF,CAEA,wBAEE,yBACF,CAEA,KAEE,cAAe,CACf,eAAgB,CAFhB,qBAGF,CAGA,oBACE,aACF,CAEA,4BAEE,UAAW,CADX,yBAEF,CAEA,0BACE,kBACF,CAEA,gCACE,eACF,CAGA,0DAIE,cAAe,CACf,eAAgB,CAFhB,YAGF,CAGA,oBACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,yBAEE,gBAAiB,CACjB,eAAgB,CAFhB,YAGF,CAGA,iBACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,sBAEE,eAAgB,CADhB,YAEF,CACF,CAEA,yBACE,MACE,gBACF,CAEA,KACE,qBAAsB,CACtB,SACF,CAEA,OAKE,kBAAmB,CADnB,YAAa,CAFb,eAAiB,CAIjB,sBAAuB,CAHvB,eAAgB,CAFhB,kBAMF,CAEA,WACE,aACF,CAEA,MAEE,iBAAmC,CAAnC,kCAAmC,CADnC,kBAEF,CAEA,WACE,YACF,CAGA,GACE,iBACF,CAEA,GACE,gBACF,CAEA,GACE,iBACF,CAGA,uBACE,gBAAiB,CAEjB,oBAAqB,CADrB,iBAEF,CAEA,YACE,qBACF,CAEA,kBAIE,aAAc,CAHd,cAAe,CACf,eAAgB,CAChB,mBAEF,CAGA,eAEE,eAAgB,CADhB,eAEF,CAEA,qBACE,8BACF,CAGA,MACE,oBACF,CAEA,KACE,YACF,CAGA,UACE,iBACF,CACF,CC1lBA,iBAGE,kBAAmB,CAEnB,kDAA6D,CAH7D,YAAa,CAKb,qDAA4D,CAH5D,sBAAuB,CAHvB,gBAAiB,CAKjB,YAEF,CAEA,WAOE,8BAAgC,CANhC,eAAiB,CACjB,kBAAmB,CACnB,gCAA0C,CAG1C,eAAgB,CAFhB,YAAa,CACb,UAGF,CAEA,mBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,cAEE,kBAAmB,CADnB,iBAEF,CAEA,iBACE,UAAW,CAEX,cAAe,CACf,eAAgB,CAFhB,eAGF,CAEA,gBACE,UAAW,CAEX,cAAe,CADf,QAEF,CAEA,YAGE,QACF,CAEA,wBALE,YAAa,CACb,qBAQF,CAJA,YAGE,OACF,CAEA,kBACE,UAAW,CAEX,cAAe,CADf,eAEF,CAEA,kBAME,kBAAmB,CAJnB,wBAAyB,CACzB,kBAAmB,CACnB,cAAe,CAHf,iBAAkB,CAIlB,uBAEF,CAEA,wBAGE,eAAiB,CADjB,oBAAqB,CAErB,8BAA8C,CAH9C,YAIF,CAEA,2BAEE,kBAAmB,CADnB,UAEF,CAEA,eAQE,+BAAiC,CAPjC,eAAgB,CAIhB,wBAAyB,CADzB,kBAAmB,CAFnB,aAAc,CAId,cAAe,CAHf,iBAAkB,CAIlB,iBAEF,CAEA,iBACE,MAAW,uBAA0B,CACrC,IAAM,0BAA6B,CACnC,IAAM,yBAA4B,CACpC,CAEA,cAWE,kBAAmB,CAVnB,kDAA6D,CAE7D,WAAY,CAEZ,kBAAmB,CAHnB,UAAY,CAMZ,cAAe,CAEf,YAAa,CAJb,cAAe,CACf,eAAgB,CAMhB,OAAQ,CADR,sBAAuB,CAEvB,eAAgB,CAVhB,iBAAkB,CAKlB,uBAMF,CAEA,mCAEE,gCAAgD,CADhD,0BAEF,CAEA,oCACE,uBACF,CAEA,uBAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAEA,iBAME,iCAAkC,CAFlC,0BAA2B,CAC3B,iBAAkB,CADlB,qBAA2B,CAF3B,WAAY,CADZ,UAMF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,cAIE,4BAA6B,CAF7B,eAAgB,CAChB,gBAAiB,CAFjB,iBAIF,CAEA,gBACE,UAAW,CAGX,cAAe,CADf,eAAgB,CADhB,cAGF,CAEA,oBACE,UAAW,CACX,cACF,CAGA,yBACE,iBACE,YACF,CAEA,WACE,iBACF,CAEA,iBACE,cACF,CACF", "sources": ["index.css", "App.css", "components/Login.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n/* Scrollbar personalizzata */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #d32f2f;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #b71c1c;\n}", "/* Reset e stili globali */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: 'Roboto', sans-serif;\n  line-height: 1.6;\n  color: #333;\n  background-color: #f5f5f5;\n}\n\n.App {\n  min-height: 100vh;\n}\n\n/* Colori tema Pizzeria Jasmin */\n:root {\n  --primary-color: #d32f2f;\n  --primary-dark: #b71c1c;\n  --secondary-color: #ff9800;\n  --success-color: #4caf50;\n  --warning-color: #ff5722;\n  --text-color: #333;\n  --text-light: #666;\n  --background-light: #f5f5f5;\n  --white: #ffffff;\n  --shadow: 0 2px 10px rgba(0,0,0,0.1);\n  --border-radius: 8px;\n}\n\n/* Header */\n.header {\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\n  color: white;\n  padding: 1rem 0;\n  box-shadow: var(--shadow);\n  position: sticky;\n  top: 0;\n  z-index: 100;\n}\n\n.header-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.logo {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1.5rem;\n  font-weight: bold;\n}\n\n.nav {\n  display: flex;\n  gap: 2rem;\n  align-items: center;\n}\n\n.nav a {\n  color: white;\n  text-decoration: none;\n  padding: 0.5rem 1rem;\n  border-radius: var(--border-radius);\n  transition: background-color 0.3s;\n}\n\n.nav a:hover {\n  background-color: rgba(255,255,255,0.1);\n}\n\n.cart-icon {\n  position: relative;\n  background: var(--secondary-color);\n  padding: 0.5rem;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: transform 0.3s;\n}\n\n.cart-icon:hover {\n  transform: scale(1.1);\n}\n\n.cart-count {\n  position: absolute;\n  top: -5px;\n  right: -5px;\n  background: var(--warning-color);\n  color: white;\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.8rem;\n  font-weight: bold;\n}\n\n/* Container principale */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 2rem 1rem;\n}\n\n/* Bottoni */\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: var(--border-radius);\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 500;\n  transition: all 0.3s;\n  text-decoration: none;\n  display: inline-block;\n  text-align: center;\n}\n\n.btn-primary {\n  background: var(--primary-color);\n  color: white;\n}\n\n.btn-primary:hover {\n  background: var(--primary-dark);\n  transform: translateY(-2px);\n}\n\n.btn-secondary {\n  background: var(--secondary-color);\n  color: white;\n}\n\n.btn-secondary:hover {\n  background: #f57c00;\n  transform: translateY(-2px);\n}\n\n.btn-success {\n  background: var(--success-color);\n  color: white;\n}\n\n.btn-success:hover {\n  background: #45a049;\n}\n\n.btn-outline {\n  background: transparent;\n  border: 2px solid var(--primary-color);\n  color: var(--primary-color);\n}\n\n.btn-outline:hover {\n  background: var(--primary-color);\n  color: white;\n}\n\n/* Card */\n.card {\n  background: white;\n  border-radius: var(--border-radius);\n  box-shadow: var(--shadow);\n  overflow: hidden;\n  transition: transform 0.3s;\n}\n\n.card img {\n  transition: transform 0.3s ease;\n}\n\n.card:hover img {\n  transform: scale(1.05);\n}\n\n.card:hover {\n  transform: translateY(-5px);\n}\n\n.card-header {\n  padding: 1rem;\n  background: var(--background-light);\n  border-bottom: 1px solid #eee;\n}\n\n.card-body {\n  padding: 1.5rem;\n}\n\n.card-footer {\n  padding: 1rem;\n  background: var(--background-light);\n  border-top: 1px solid #eee;\n}\n\n/* Grid */\n.grid {\n  display: grid;\n  gap: 1.5rem;\n}\n\n.grid-2 {\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n}\n\n.grid-3 {\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n}\n\n.grid-4 {\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n}\n\n/* Form */\n.form-group {\n  margin-bottom: 1rem;\n}\n\n.form-label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: var(--text-color);\n}\n\n.form-input {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #ddd;\n  border-radius: var(--border-radius);\n  font-size: 1rem;\n  transition: border-color 0.3s;\n}\n\n.form-input:focus {\n  outline: none;\n  border-color: var(--primary-color);\n}\n\n.form-select {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #ddd;\n  border-radius: var(--border-radius);\n  font-size: 1rem;\n  background: white;\n  cursor: pointer;\n}\n\n/* Utility classes */\n.text-center { text-align: center; }\n.text-left { text-align: left; }\n.text-right { text-align: right; }\n\n.mt-1 { margin-top: 0.5rem; }\n.mt-2 { margin-top: 1rem; }\n.mt-3 { margin-top: 1.5rem; }\n.mb-1 { margin-bottom: 0.5rem; }\n.mb-2 { margin-bottom: 1rem; }\n.mb-3 { margin-bottom: 1.5rem; }\n\n.p-1 { padding: 0.5rem; }\n.p-2 { padding: 1rem; }\n.p-3 { padding: 1.5rem; }\n\n.flex { display: flex; }\n.flex-center { display: flex; align-items: center; justify-content: center; }\n.flex-between { display: flex; align-items: center; justify-content: space-between; }\n.flex-column { flex-direction: column; }\n.gap-1 { gap: 0.5rem; }\n.gap-2 { gap: 1rem; }\n.gap-3 { gap: 1.5rem; }\n\n.w-full { width: 100%; }\n.h-full { height: 100%; }\n\n/* Status badges */\n.status-badge {\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: bold;\n  text-transform: uppercase;\n}\n\n.status-ricevuto {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.status-preparazione {\n  background: #fff3e0;\n  color: #f57c00;\n}\n\n.status-pronto {\n  background: #e8f5e8;\n  color: #4caf50;\n}\n\n.status-consegnato {\n  background: #f3e5f5;\n  color: #9c27b0;\n}\n\n/* Animazioni */\n@keyframes fadeIn {\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n@keyframes pulse {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n}\n\n.fade-in {\n  animation: fadeIn 0.5s ease-out;\n}\n\n.pulse {\n  animation: pulse 2s infinite;\n}\n\n/* Form Validation Styles */\n.form-group {\n  position: relative;\n  margin-bottom: 1.5rem;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  width: 100%;\n  padding: 1rem;\n  border: 2px solid #e0e0e0;\n  border-radius: var(--border-radius);\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background: white;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: var(--primary-color);\n  box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.1);\n}\n\n.form-group.error input,\n.form-group.error select,\n.form-group.error textarea {\n  border-color: var(--warning-color);\n  background-color: #fff5f5;\n}\n\n.form-group.success input,\n.form-group.success select,\n.form-group.success textarea {\n  border-color: var(--success-color);\n  background-color: #f0fff4;\n}\n\n.error-message {\n  color: var(--warning-color);\n  font-size: 0.875rem;\n  margin-top: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  animation: fadeIn 0.3s ease;\n}\n\n.error-icon {\n  color: var(--warning-color);\n  position: absolute;\n  right: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  pointer-events: none;\n}\n\n.success-icon {\n  color: var(--success-color);\n  position: absolute;\n  right: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  pointer-events: none;\n}\n\n/* Loading States */\n.btn.loading {\n  position: relative;\n  color: transparent;\n}\n\n.btn.loading::after {\n  content: '';\n  position: absolute;\n  width: 20px;\n  height: 20px;\n  top: 50%;\n  left: 50%;\n  margin-left: -10px;\n  margin-top: -10px;\n  border: 2px solid transparent;\n  border-top-color: currentColor;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n\n/* Error Color Variable */\n:root {\n  --error-color: #f44336;\n}\n\n/* Responsive */\n@media (max-width: 768px) {\n  .container {\n    padding: 1rem;\n  }\n  \n  .header-content {\n    padding: 0 1rem;\n  }\n  \n  .nav {\n    gap: 1rem;\n  }\n  \n  .grid-2 {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n  \n  .grid-3,\n  .grid-4 {\n    grid-template-columns: 1fr;\n  }\n  \n  .btn {\n    padding: 0.75rem 1.5rem;\n    font-size: 1rem;\n    min-height: 48px; /* Touch-friendly */\n  }\n  \n  /* Checkout Mobile Optimization */\n  .checkout-container {\n    padding: 0.5rem;\n  }\n  \n  .checkout-container .grid-2 {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n  \n  .checkout-container .card {\n    margin-bottom: 1rem;\n  }\n  \n  .checkout-container .sticky-top {\n    position: static;\n  }\n  \n  /* Form inputs mobile */\n  .form-group input,\n  .form-group select,\n  .form-group textarea {\n    padding: 1rem;\n    font-size: 16px; /* Prevents zoom on iOS */\n    min-height: 48px;\n  }\n  \n  /* Order type buttons mobile */\n  .order-type-buttons {\n    display: flex;\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n  \n  .order-type-buttons .btn {\n    padding: 1rem;\n    font-size: 1.1rem;\n    min-height: 56px;\n  }\n  \n  /* Payment method buttons mobile */\n  .payment-methods {\n    display: flex;\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n  \n  .payment-methods .btn {\n    padding: 1rem;\n    min-height: 56px;\n  }\n}\n\n@media (max-width: 480px) {\n  .logo {\n    font-size: 1.2rem;\n  }\n  \n  .nav {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n  \n  .nav a {\n    padding: 0.5rem 1rem;\n    font-size: 0.9rem;\n    min-height: 44px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .container {\n    padding: 0.5rem;\n  }\n  \n  .card {\n    margin-bottom: 1rem;\n    border-radius: var(--border-radius);\n  }\n  \n  .card-body {\n    padding: 1rem;\n  }\n  \n  /* Typography mobile */\n  h1 {\n    font-size: 1.75rem;\n  }\n  \n  h2 {\n    font-size: 1.5rem;\n  }\n  \n  h3 {\n    font-size: 1.25rem;\n  }\n  \n  /* Checkout specific mobile styles */\n  .checkout-container h1 {\n    font-size: 1.5rem;\n    text-align: center;\n    margin-bottom: 1.5rem;\n  }\n  \n  .form-group {\n    margin-bottom: 1.25rem;\n  }\n  \n  .form-group label {\n    font-size: 1rem;\n    font-weight: 600;\n    margin-bottom: 0.5rem;\n    display: block;\n  }\n  \n  /* Order summary mobile */\n  .order-summary {\n    position: static;\n    margin-top: 1rem;\n  }\n  \n  .order-summary .card {\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  }\n  \n  /* Touch-friendly spacing */\n  .mb-3 {\n    margin-bottom: 1.5rem;\n  }\n  \n  .p-3 {\n    padding: 1rem;\n  }\n  \n  /* Improved button spacing */\n  .btn + .btn {\n    margin-top: 0.75rem;\n  }\n}", ".login-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n\n.login-box {\n  background: white;\n  border-radius: 20px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  padding: 40px;\n  width: 100%;\n  max-width: 400px;\n  animation: slideUp 0.6s ease-out;\n}\n\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.login-header h2 {\n  color: #333;\n  margin: 0 0 10px 0;\n  font-size: 28px;\n  font-weight: 700;\n}\n\n.login-header p {\n  color: #666;\n  margin: 0;\n  font-size: 14px;\n}\n\n.login-form {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.form-group label {\n  color: #333;\n  font-weight: 600;\n  font-size: 14px;\n}\n\n.form-group input {\n  padding: 12px 16px;\n  border: 2px solid #e1e5e9;\n  border-radius: 10px;\n  font-size: 16px;\n  transition: all 0.3s ease;\n  background: #f8f9fa;\n}\n\n.form-group input:focus {\n  outline: none;\n  border-color: #667eea;\n  background: white;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.form-group input:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.error-message {\n  background: #fee;\n  color: #c53030;\n  padding: 12px 16px;\n  border-radius: 10px;\n  border: 1px solid #fed7d7;\n  font-size: 14px;\n  text-align: center;\n  animation: shake 0.5s ease-in-out;\n}\n\n@keyframes shake {\n  0%, 100% { transform: translateX(0); }\n  25% { transform: translateX(-5px); }\n  75% { transform: translateX(5px); }\n}\n\n.login-button {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 14px 20px;\n  border-radius: 10px;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  margin-top: 10px;\n}\n\n.login-button:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n}\n\n.login-button:active:not(:disabled) {\n  transform: translateY(0);\n}\n\n.login-button:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.loading-spinner {\n  width: 16px;\n  height: 16px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-top: 2px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.login-footer {\n  text-align: center;\n  margin-top: 30px;\n  padding-top: 20px;\n  border-top: 1px solid #e1e5e9;\n}\n\n.login-footer p {\n  color: #666;\n  margin: 0 0 5px 0;\n  font-weight: 600;\n  font-size: 14px;\n}\n\n.login-footer small {\n  color: #999;\n  font-size: 12px;\n}\n\n/* Responsive */\n@media (max-width: 480px) {\n  .login-container {\n    padding: 10px;\n  }\n  \n  .login-box {\n    padding: 30px 20px;\n  }\n  \n  .login-header h2 {\n    font-size: 24px;\n  }\n}"], "names": [], "sourceRoot": ""}