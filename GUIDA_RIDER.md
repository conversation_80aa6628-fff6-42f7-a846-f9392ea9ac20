# 🏍️ Guida per i Rider - Pizzeria Jasmin

## 📱 Come Funziona il Sistema

### **1. Accesso alla Dashboard**
- Il proprietario/pizzaiolo accede alla dashboard con login
- Nell<PERSON> se<PERSON> "Stato Rider" vede tutti i rider disponibili
- I rider sono preconfigurati: <PERSON> e <PERSON>

### **2. Assegnazione Consegne**
Quando un ordine è pronto per la consegna:
1. Il pizzaiolo clicca **"Assegna Rider"** sull'ordine
2. Si apre un popup con i rider disponibili
3. Seleziona il rider libero
4. L'ordine passa in stato **"In Consegna"**

### **3. Informazioni per il Rider**
Ogni ordine di consegna mostra:
- **📍 Indirizzo completo** del cliente
- **📞 Telefono** con link per chiamata diretta
- **🗺️ Distanza stimata** dalla pizzeria
- **💰 Importo totale** dell'ordine
- **📝 Note speciali** del cliente

### **4. Navigazione GPS**
- **Pulsante "Naviga con Google Maps"** in ogni ordine
- Apre automaticamente Google Maps con l'indirizzo
- Funziona sia su app mobile che browser web
- Calcolo automatico del percorso ottimale

### **5. Zone di Consegna e Distanze**
| Zona | Distanza | Costo Consegna |
|------|----------|----------------|
| Missaglia | 0 km | Gratuita (sopra €15) |
| Casatenovo | 3 km | €2.50 |
| Barzanò | 5 km | €3.00 |
| Cremella | 7 km | €3.50 |
| Monticello Brianza | 8 km | €4.00 |
| Viganò | 10 km | €4.50 |
| Altro | ~12 km | €5.00 |

### **6. Completamento Consegna**
Quando il rider consegna l'ordine:
1. Il pizzaiolo clicca **"Completata"** nella dashboard
2. Il rider torna automaticamente **"Disponibile"**
3. L'ordine passa in stato **"Consegnato"**
4. Il rider può ricevere una nuova assegnazione

## 🔧 **Funzionalità Avanzate**

### **Chiamata Diretta Cliente**
- Ogni ordine ha il **numero di telefono** del cliente
- Click sul numero per chiamare direttamente
- Utile per confermare indirizzo o citofono

### **Note Speciali**
- Le note del cliente sono evidenziate in **giallo**
- Contengono informazioni importanti:
  - Numero citofono
  - Piano dell'abitazione
  - Istruzioni particolari
  - Richieste speciali

### **Stato in Tempo Reale**
- La dashboard si aggiorna automaticamente
- I rider vedono sempre lo stato attuale
- Notifiche per ordini urgenti o in ritardo

## 📋 **Procedura Standard**

### **Per il Pizzaiolo:**
1. Ordine ricevuto → **"Nuovo Ordine"**
2. Inizia preparazione → **"In Preparazione"**
3. Pizza pronta → **"Pronto per Consegna"**
4. Assegna rider → **"In Consegna"**
5. Consegna completata → **"Consegnato"**

### **Per il Rider:**
1. Ricevi assegnazione dal pizzaiolo
2. Controlla indirizzo e note cliente
3. Clicca **"Naviga con Google Maps"**
4. Chiama cliente se necessario
5. Effettua la consegna
6. Conferma completamento al pizzaiolo

## 🚨 **Situazioni Speciali**

### **Cliente Non Trovato**
- Chiamare il numero fornito
- Verificare indirizzo con il cliente
- Contattare la pizzeria se necessario

### **Problemi di Navigazione**
- Usare il pulsante "Naviga" per GPS automatico
- In caso di problemi, chiamare il cliente
- Indirizzo sempre completo di città

### **Ordini Urgenti**
- Ordini in ritardo sono evidenziati in **rosso**
- Priorità agli ordini più vecchi
- Notifiche automatiche per urgenze

## 📞 **Contatti**

**Pizzeria Jasmin**
- 📍 Via Marconi, 5 - Missaglia (LC)
- ☎️ 039 9240339
- 🕐 Orari: 17:30-22:00

**Rider Preconfigurati:**
- 🏍️ Marco Bianchi: 339-123-4567
- 🏍️ Luca Rossi: 338-987-6543

---

*Sistema sviluppato per ottimizzare le consegne e garantire la massima soddisfazione del cliente* 🍕
