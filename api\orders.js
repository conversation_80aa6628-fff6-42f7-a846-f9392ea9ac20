import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

export default async function handler(req, res) {
  // Abilita CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    if (req.method === 'GET') {
      // Ottieni tutti gli ordini
      const result = await pool.query(`
        SELECT o.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        ORDER BY o.created_at DESC
        LIMIT 50
      `);

      res.status(200).json(result.rows);

    } else if (req.method === 'POST') {
      // Crea nuovo ordine
      const {
        customer,
        items,
        total_amount,
        delivery_type,
        delivery_time,
        notes
      } = req.body;

      console.log('Dati ricevuti:', { customer, items, total_amount, delivery_type });

      // Validazione base
      if (!customer || !items || !total_amount || !delivery_type) {
        return res.status(400).json({
          error: 'Dati mancanti',
          required: ['customer', 'items', 'total_amount', 'delivery_type']
        });
      }

      // Inizia transazione
      const client = await pool.connect();

      try {
        await client.query('BEGIN');

        // Crea o trova cliente
        let customerId;
        const existingCustomer = await client.query(
          'SELECT id FROM customers WHERE email = $1',
          [customer.email]
        );

        if (existingCustomer.rows.length > 0) {
          customerId = existingCustomer.rows[0].id;
          // Aggiorna dati cliente
          await client.query(
            'UPDATE customers SET name = $1, phone = $2, address = $3 WHERE id = $4',
            [customer.name, customer.phone, customer.address || '', customerId]
          );
        } else {
          const newCustomer = await client.query(
            'INSERT INTO customers (name, email, phone, address) VALUES ($1, $2, $3, $4) RETURNING id',
            [customer.name, customer.email, customer.phone, customer.address || '']
          );
          customerId = newCustomer.rows[0].id;
        }

        // Genera numero ordine
        const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`;

        // Crea ordine
        const orderResult = await client.query(`
          INSERT INTO orders (
            customer_id, order_number, total_amount, delivery_type,
            delivery_address, delivery_time, notes, status
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'pending')
          RETURNING id
        `, [
          customerId, orderNumber, total_amount, delivery_type,
          customer.address || '', delivery_time || 'ASAP', notes || ''
        ]);

        const orderId = orderResult.rows[0].id;

        // Inserisci items dell'ordine
        for (const item of items) {
          await client.query(`
            INSERT INTO order_items (
              order_id, product_id, quantity, unit_price, total_price, customizations
            ) VALUES ($1, $2, $3, $4, $5, $6)
          `, [
            orderId, item.id, item.quantity, item.price,
            item.price * item.quantity, JSON.stringify(item.customizations || {})
          ]);
        }

        await client.query('COMMIT');

        res.status(201).json({
          success: true,
          id: orderId,
          order_number: orderNumber,
          customer_id: customerId,
          total_amount,
          delivery_type,
          delivery_time: delivery_time || 'ASAP',
          notes,
          items,
          message: 'Ordine creato con successo'
        });

      } catch (error) {
        await client.query('ROLLBACK');
        throw error;
      } finally {
        client.release();
      }

    } else {
      res.status(405).json({ error: 'Metodo non supportato' });
    }
  } catch (error) {
    console.error('Errore API ordini:', error);
    res.status(500).json({
      error: 'Errore interno del server',
      details: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
}
