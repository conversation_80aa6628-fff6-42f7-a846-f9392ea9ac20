// Configurazione dell'applicazione - Aggiornato 16/06/2025 12:00
const config = {
  // URL del backend API - VERSIONE FINALE
  API_BASE_URL: process.env.NODE_ENV === 'production'
    ? 'https://pizzeria-jasmin.vercel.app'
    : 'http://localhost:3001',
  
  // URL delle API specifiche
  API_ENDPOINTS: {
    // API Database
    ORDERS: '/api/orders',
    CUSTOMERS: '/api/customers',
    PRODUCTS: '/api/products',
    
    // API Stripe
    STRIPE_CREATE_CUSTOMER: '/api/stripe/create-customer',
    STRIPE_CREATE_PRODUCT: '/api/stripe/create-product',
    STRIPE_CREATE_PRICE: '/api/stripe/create-price',
    STRIPE_CREATE_PAYMENT_LINK: '/api/stripe/create-payment-link',
    STRIPE_WEBHOOK: '/api/stripe/webhook'
  },
  
  // Configurazione dell'applicazione
  APP: {
    NAME: 'Pizzeria Jasmin',
    VERSION: '1.0.0',
    DESCRIPTION: 'Sistema di ordinazione online per Pizzeria Jasmin - Missaglia'
  },
  
  // Configurazione della pizzeria
  PIZZERIA: {
    NAME: 'Pizzeria Jasmin',
    ADDRESS: 'Via Marconi, 5, Missaglia (LC)',
    PHONE: '039 9240339',
    HOURS: '17:30-22:00',
    SPECIALTIES: ['Pizza', 'Kebab']
  },
  
  // Zone di consegna e tariffe
  DELIVERY_ZONES: {
    'Missaglia': 0,
    'Casatenovo': 2.50,
    'Barzanò': 3.00,
    'Cremella': 3.50,
    'Monticello Brianza': 4.00,
    'Viganò': 4.50,
    'Altro': 5.00
  },
  
  // Configurazione ordini
  ORDER_CONFIG: {
    MIN_ORDER_FOR_FREE_DELIVERY: 15.00, // Solo per Missaglia
    DEFAULT_PREPARATION_TIME: 10, // minuti
    PIZZA_PREPARATION_TIME: 3, // minuti per pizza
    KEBAB_PREPARATION_TIME: 2, // minuti per kebab
    DELIVERY_TIME: 20 // minuti aggiuntivi per la consegna
  },
  
  // Personalizzazioni e prezzi aggiuntivi
  CUSTOMIZATIONS: {
    SIZE: {
      'grande': 2.00
    },
    DOUGH: {
      'integrale': 1.00,
      'senza-glutine': 3.00
    }
  }
};

// Funzione helper per costruire URL completi
export const buildApiUrl = (endpoint) => {
  return `${config.API_BASE_URL}${endpoint}`;
};

// Funzione helper per ottenere l'URL di un endpoint specifico
export const getApiEndpoint = (endpointKey) => {
  const endpoint = config.API_ENDPOINTS[endpointKey];
  if (!endpoint) {
    throw new Error(`Endpoint '${endpointKey}' non trovato nella configurazione`);
  }
  return buildApiUrl(endpoint);
};

export default config;
