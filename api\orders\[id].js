import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

export default async function handler(req, res) {
  const { id } = req.query;

  // Abilita CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    if (req.method === 'GET') {
      // Ottieni ordine specifico con items
      const orderResult = await pool.query(`
        SELECT o.*, c.name as customer_name, c.email as customer_email, 
               c.phone as customer_phone, c.address as customer_address
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        WHERE o.id = $1
      `, [id]);

      if (orderResult.rows.length === 0) {
        return res.status(404).json({ error: 'Ordine non trovato' });
      }

      const order = orderResult.rows[0];

      // Ottieni items dell'ordine
      const itemsResult = await pool.query(`
        SELECT oi.*, p.name as product_name, p.description as product_description
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = $1
      `, [id]);

      order.items = itemsResult.rows.map(item => ({
        ...item,
        customizations: JSON.parse(item.customizations || '{}')
      }));

      res.status(200).json(order);

    } else if (req.method === 'PATCH') {
      // Aggiorna stato ordine
      const { status } = req.body;

      if (!status) {
        return res.status(400).json({
          error: 'Status richiesto',
          required: ['status']
        });
      }

      console.log(`🔄 Aggiornamento stato ordine ${id} a: ${status}`);

      // Valida status
      const validStatuses = ['pending', 'preparing', 'ready_pickup', 'ready_delivery', 'out_for_delivery', 'completed', 'cancelled'];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({ error: 'Status non valido', validStatuses });
      }

      const result = await pool.query(
        'UPDATE orders SET status = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
        [status, id]
      );

      if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Ordine non trovato' });
      }

      console.log(`✅ Stato ordine ${id} aggiornato a: ${status}`);

      res.status(200).json({
        success: true,
        order: result.rows[0],
        message: `Stato ordine aggiornato a: ${status}`
      });

    } else {
      res.status(405).json({ error: 'Metodo non supportato' });
    }
  } catch (error) {
    console.error('Errore API ordine singolo:', error);
    res.status(500).json({ 
      error: 'Errore interno del server',
      details: error.message 
    });
  }
}
