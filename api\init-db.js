import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Solo POST supportato' });
  }

  try {
    // Crea tabelle
    await pool.query(`
      CREATE TABLE IF NOT EXISTS customers (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        phone VARCHAR(50),
        address TEXT,
        stripe_customer_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    await pool.query(`
      CREATE TABLE IF NOT EXISTS products (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL,
        category VARCHAR(100) NOT NULL,
        image_url VARCHAR(500),
        available BOOLEAN DEFAULT true,
        stripe_product_id VARCHAR(255),
        stripe_price_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    await pool.query(`
      CREATE TABLE IF NOT EXISTS orders (
        id SERIAL PRIMARY KEY,
        customer_id INTEGER REFERENCES customers(id),
        order_number VARCHAR(100) UNIQUE NOT NULL,
        total_amount DECIMAL(10,2) NOT NULL,
        status VARCHAR(50) DEFAULT 'pending',
        delivery_type VARCHAR(20) NOT NULL,
        delivery_address TEXT,
        delivery_time VARCHAR(50),
        payment_status VARCHAR(50) DEFAULT 'pending',
        stripe_payment_intent_id VARCHAR(255),
        notes TEXT,
        rider_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    await pool.query(`
      CREATE TABLE IF NOT EXISTS order_items (
        id SERIAL PRIMARY KEY,
        order_id INTEGER REFERENCES orders(id),
        product_id INTEGER REFERENCES products(id),
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        customizations JSONB DEFAULT '{}'
      )
    `);

    // Inserisci prodotti di esempio se non esistono
    const productCount = await pool.query('SELECT COUNT(*) FROM products');
    
    if (parseInt(productCount.rows[0].count) === 0) {
      const products = [
        ['Pizza Margherita', 'Pomodoro, mozzarella, basilico', 8.50, 'pizza', '/images/pizza-margherita.svg'],
        ['Pizza Marinara', 'Pomodoro, aglio, origano', 7.00, 'pizza', '/images/pizza-marinara.svg'],
        ['Pizza Diavola', 'Pomodoro, mozzarella, salame piccante', 10.00, 'pizza', '/images/pizza-diavola.svg'],
        ['Pizza Capricciosa', 'Pomodoro, mozzarella, prosciutto, funghi, carciofi, olive', 12.00, 'pizza', '/images/pizza-capricciosa.svg'],
        ['Pizza Quattro Formaggi', 'Mozzarella, gorgonzola, parmigiano, fontina', 11.50, 'pizza', '/images/pizza-quattro-formaggi.svg'],
        ['Pizza Prosciutto e Funghi', 'Pomodoro, mozzarella, prosciutto, funghi', 10.50, 'pizza', '/images/pizza-prosciutto-funghi.svg'],
        ['Pizza Quattro Stagioni', 'Pomodoro, mozzarella, prosciutto, funghi, carciofi, olive', 12.50, 'pizza', '/images/pizza-quattro-stagioni.svg'],
        ['Pizza Vegetariana', 'Pomodoro, mozzarella, verdure miste', 9.50, 'pizza', '/images/pizza-vegetariana.svg'],
        ['Pane Kebab', 'Pane, carne kebab, verdure, salse', 6.50, 'kebab', '/images/kebab-pane.svg']
      ];

      for (const product of products) {
        await pool.query(
          'INSERT INTO products (name, description, price, category, image_url) VALUES ($1, $2, $3, $4, $5)',
          product
        );
      }
    }

    res.status(200).json({ 
      message: 'Database inizializzato con successo',
      tables: ['customers', 'products', 'orders', 'order_items'],
      products_inserted: parseInt(productCount.rows[0].count) === 0 ? 9 : 0
    });

  } catch (error) {
    console.error('Errore inizializzazione database:', error);
    res.status(500).json({ 
      error: 'Errore inizializzazione database',
      details: error.message 
    });
  }
}
