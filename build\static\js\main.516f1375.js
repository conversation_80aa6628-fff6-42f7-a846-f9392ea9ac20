/*! For license information please see main.516f1375.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},153:(e,t,n)=>{var r=n(43),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,o=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,l={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,r)&&!s.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:a,type:e,key:c,ref:u,props:l,_owner:o.current}}t.Fragment=l,t.jsx=c,t.jsxs=c},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),o=Symbol.for("react.provider"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),m=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var x=b.prototype=new y;x.constructor=b,h(x,v.prototype),x.isPureReactComponent=!0;var j=Array.isArray,S=Object.prototype.hasOwnProperty,N={current:null},w={key:!0,ref:!0,__self:!0,__source:!0};function k(e,t,r){var a,l={},i=null,o=null;if(null!=t)for(a in void 0!==t.ref&&(o=t.ref),void 0!==t.key&&(i=""+t.key),t)S.call(t,a)&&!w.hasOwnProperty(a)&&(l[a]=t[a]);var s=arguments.length-2;if(1===s)l.children=r;else if(1<s){for(var c=Array(s),u=0;u<s;u++)c[u]=arguments[u+2];l.children=c}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===l[a]&&(l[a]=s[a]);return{$$typeof:n,type:e,key:i,ref:o,props:l,_owner:N.current}}function z(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var C=/\/+/g;function E(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function _(e,t,a,l,i){var o=typeof e;"undefined"!==o&&"boolean"!==o||(e=null);var s=!1;if(null===e)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return i=i(s=e),e=""===l?"."+E(s,0):l,j(i)?(a="",null!=e&&(a=e.replace(C,"$&/")+"/"),_(i,t,a,"",(function(e){return e}))):null!=i&&(z(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,a+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(C,"$&/")+"/")+e)),t.push(i)),1;if(s=0,l=""===l?".":l+":",j(e))for(var c=0;c<e.length;c++){var u=l+E(o=e[c],c);s+=_(o,t,a,u,i)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=m&&e[m]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(o=e.next()).done;)s+=_(o=o.value,t,a,u=l+E(o,c++),i);else if("object"===o)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function T(e,t,n){if(null==e)return e;var r=[],a=0;return _(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function P(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var O={current:null},R={transition:null},I={ReactCurrentDispatcher:O,ReactCurrentBatchConfig:R,ReactCurrentOwner:N};function L(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:T,forEach:function(e,t,n){T(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return T(e,(function(){t++})),t},toArray:function(e){return T(e,(function(e){return e}))||[]},only:function(e){if(!z(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=i,t.PureComponent=b,t.StrictMode=l,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=I,t.act=L,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=h({},e.props),l=e.key,i=e.ref,o=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,o=N.current),void 0!==t.key&&(l=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(c in t)S.call(t,c)&&!w.hasOwnProperty(c)&&(a[c]=void 0===t[c]&&void 0!==s?s[c]:t[c])}var c=arguments.length-2;if(1===c)a.children=r;else if(1<c){s=Array(c);for(var u=0;u<c;u++)s[u]=arguments[u+2];a.children=s}return{$$typeof:n,type:e.type,key:l,ref:i,props:a,_owner:o}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:o,_context:e},e.Consumer=e},t.createElement=k,t.createFactory=function(e){var t=k.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=z,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:P}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=R.transition;R.transition={};try{e()}finally{R.transition=t}},t.unstable_act=L,t.useCallback=function(e,t){return O.current.useCallback(e,t)},t.useContext=function(e){return O.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return O.current.useDeferredValue(e)},t.useEffect=function(e,t){return O.current.useEffect(e,t)},t.useId=function(){return O.current.useId()},t.useImperativeHandle=function(e,t,n){return O.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return O.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return O.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return O.current.useMemo(e,t)},t.useReducer=function(e,t,n){return O.current.useReducer(e,t,n)},t.useRef=function(e){return O.current.useRef(e)},t.useState=function(e){return O.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return O.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return O.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,i=a>>>1;r<i;){var o=2*(r+1)-1,s=e[o],c=o+1,u=e[c];if(0>l(s,n))c<a&&0>l(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[o]=n,r=o);else{if(!(c<a&&0>l(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var o=Date,s=o.now();t.unstable_now=function(){return o.now()-s}}var c=[],u=[],d=1,f=null,m=3,p=!1,h=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(u);null!==t;){if(null===t.callback)a(u);else{if(!(t.startTime<=e))break;a(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function j(e){if(g=!1,x(e),!h)if(null!==r(c))h=!0,R(S);else{var t=r(u);null!==t&&I(j,t.startTime-e)}}function S(e,n){h=!1,g&&(g=!1,y(z),z=-1),p=!0;var l=m;try{for(x(n),f=r(c);null!==f&&(!(f.expirationTime>n)||e&&!_());){var i=f.callback;if("function"===typeof i){f.callback=null,m=f.priorityLevel;var o=i(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof o?f.callback=o:f===r(c)&&a(c),x(n)}else a(c);f=r(c)}if(null!==f)var s=!0;else{var d=r(u);null!==d&&I(j,d.startTime-n),s=!1}return s}finally{f=null,m=l,p=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var N,w=!1,k=null,z=-1,C=5,E=-1;function _(){return!(t.unstable_now()-E<C)}function T(){if(null!==k){var e=t.unstable_now();E=e;var n=!0;try{n=k(!0,e)}finally{n?N():(w=!1,k=null)}}else w=!1}if("function"===typeof b)N=function(){b(T)};else if("undefined"!==typeof MessageChannel){var P=new MessageChannel,O=P.port2;P.port1.onmessage=T,N=function(){O.postMessage(null)}}else N=function(){v(T,0)};function R(e){k=e,w||(w=!0,N())}function I(e,n){z=v((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){h||p||(h=!0,R(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return m},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(m){case 1:case 2:case 3:var t=3;break;default:t=m}var n=m;m=t;try{return e()}finally{m=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=m;m=e;try{return t()}finally{m=n}},t.unstable_scheduleCallback=function(e,a,l){var i=t.unstable_now();switch("object"===typeof l&&null!==l?l="number"===typeof(l=l.delay)&&0<l?i+l:i:l=i,e){case 1:var o=-1;break;case 2:o=250;break;case 5:o=1073741823;break;case 4:o=1e4;break;default:o=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:o=l+o,sortIndex:-1},l>i?(e.sortIndex=l,n(u,e),null===r(c)&&e===r(u)&&(g?(y(z),z=-1):g=!0,I(j,l-i))):(e.sortIndex=o,n(c,e),h||p||(h=!0,R(S))),e},t.unstable_shouldYield=_,t.unstable_wrapCallback=function(e){var t=m;return function(){var n=m;m=t;try{return e.apply(this,arguments)}finally{m=n}}}},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},579:(e,t,n)=>{e.exports=n(153)},730:(e,t,n)=>{var r=n(43),a=n(853);function l(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,o={};function s(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(o[e]=t,e=0;e<t.length;e++)i.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,m={},p={};function h(e,t,n,r,a,l,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=i}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){g[e]=new h(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];g[t]=new h(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){g[e]=new h(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){g[e]=new h(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){g[e]=new h(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){g[e]=new h(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){g[e]=new h(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){g[e]=new h(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){g[e]=new h(e,5,!1,e.toLowerCase(),null,!1,!1)}));var v=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(p,e)||!d.call(m,e)&&(f.test(e)?p[e]=!0:(m[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(v,y);g[t]=new h(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(v,y);g[t]=new h(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(v,y);g[t]=new h(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!1,!1)})),g.xlinkHref=new h("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!0,!0)}));var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,j=Symbol.for("react.element"),S=Symbol.for("react.portal"),N=Symbol.for("react.fragment"),w=Symbol.for("react.strict_mode"),k=Symbol.for("react.profiler"),z=Symbol.for("react.provider"),C=Symbol.for("react.context"),E=Symbol.for("react.forward_ref"),_=Symbol.for("react.suspense"),T=Symbol.for("react.suspense_list"),P=Symbol.for("react.memo"),O=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var R=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var I=Symbol.iterator;function L(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=I&&e[I]||e["@@iterator"])?e:null}var D,M=Object.assign;function F(e){if(void 0===D)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);D=t&&t[1]||""}return"\n"+D+e}var A=!1;function U(e,t){if(!e||A)return"";A=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&"string"===typeof c.stack){for(var a=c.stack.split("\n"),l=r.stack.split("\n"),i=a.length-1,o=l.length-1;1<=i&&0<=o&&a[i]!==l[o];)o--;for(;1<=i&&0<=o;i--,o--)if(a[i]!==l[o]){if(1!==i||1!==o)do{if(i--,0>--o||a[i]!==l[o]){var s="\n"+a[i].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=i&&0<=o);break}}}finally{A=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?F(e):""}function B(e){switch(e.tag){case 5:return F(e.type);case 16:return F("Lazy");case 13:return F("Suspense");case 19:return F("SuspenseList");case 0:case 2:case 15:return e=U(e.type,!1);case 11:return e=U(e.type.render,!1);case 1:return e=U(e.type,!0);default:return""}}function W(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case N:return"Fragment";case S:return"Portal";case k:return"Profiler";case w:return"StrictMode";case _:return"Suspense";case T:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case C:return(e.displayName||"Context")+".Consumer";case z:return(e._context.displayName||"Context")+".Provider";case E:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case P:return null!==(t=e.displayName||null)?t:W(e.type)||"Memo";case O:t=e._payload,e=e._init;try{return W(e(t))}catch(n){}}return null}function V(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return W(t);case 8:return t===w?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function $(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function H(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Q(e){e._valueTracker||(e._valueTracker=function(e){var t=H(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=H(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function J(e,t){var n=t.checked;return M({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Y(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=$(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function G(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function Z(e,t){G(e,t);var n=$(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,$(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function X(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+$(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(l(91));return M({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(l(92));if(te(n)){if(1<n.length)throw Error(l(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:$(n)}}function le(e,t){var n=$(t.value),r=$(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function oe(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?oe(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue,de=(ue=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ue(e,t)}))}:ue);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var me={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function he(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||me.hasOwnProperty(e)&&me[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=he(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(me).forEach((function(e){pe.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),me[t]=me[e]}))}));var ve=M({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(l(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(l(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(l(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(l(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function je(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,Ne=null,we=null;function ke(e){if(e=ba(e)){if("function"!==typeof Se)throw Error(l(280));var t=e.stateNode;t&&(t=ja(t),Se(e.stateNode,e.type,t))}}function ze(e){Ne?we?we.push(e):we=[e]:Ne=e}function Ce(){if(Ne){var e=Ne,t=we;if(we=Ne=null,ke(e),t)for(e=0;e<t.length;e++)ke(t[e])}}function Ee(e,t){return e(t)}function _e(){}var Te=!1;function Pe(e,t,n){if(Te)return e(t,n);Te=!0;try{return Ee(e,t,n)}finally{Te=!1,(null!==Ne||null!==we)&&(_e(),Ce())}}function Oe(e,t){var n=e.stateNode;if(null===n)return null;var r=ja(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(l(231,t,typeof n));return n}var Re=!1;if(u)try{var Ie={};Object.defineProperty(Ie,"passive",{get:function(){Re=!0}}),window.addEventListener("test",Ie,Ie),window.removeEventListener("test",Ie,Ie)}catch(ue){Re=!1}function Le(e,t,n,r,a,l,i,o,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var De=!1,Me=null,Fe=!1,Ae=null,Ue={onError:function(e){De=!0,Me=e}};function Be(e,t,n,r,a,l,i,o,s){De=!1,Me=null,Le.apply(Ue,arguments)}function We(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Ve(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function $e(e){if(We(e)!==e)throw Error(l(188))}function He(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=We(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var i=a.alternate;if(null===i){if(null!==(r=a.return)){n=r;continue}break}if(a.child===i.child){for(i=a.child;i;){if(i===n)return $e(a),e;if(i===r)return $e(a),t;i=i.sibling}throw Error(l(188))}if(n.return!==r.return)n=a,r=i;else{for(var o=!1,s=a.child;s;){if(s===n){o=!0,n=a,r=i;break}if(s===r){o=!0,r=a,n=i;break}s=s.sibling}if(!o){for(s=i.child;s;){if(s===n){o=!0,n=i,r=a;break}if(s===r){o=!0,r=i,n=a;break}s=s.sibling}if(!o)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}(e))?Qe(e):null}function Qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Qe(e);if(null!==t)return t;e=e.sibling}return null}var qe=a.unstable_scheduleCallback,Ke=a.unstable_cancelCallback,Je=a.unstable_shouldYield,Ye=a.unstable_requestPaint,Ge=a.unstable_now,Ze=a.unstable_getCurrentPriorityLevel,Xe=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,lt=null;var it=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(ot(e)/st|0)|0},ot=Math.log,st=Math.LN2;var ct=64,ut=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,l=e.pingedLanes,i=268435455&n;if(0!==i){var o=i&~a;0!==o?r=dt(o):0!==(l&=i)&&(r=dt(l))}else 0!==(i=n&~a)?r=dt(i):0!==l&&(r=dt(l));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(l=t&-t)||16===a&&0!==(4194240&l)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-it(t)),r|=e[n],t&=~a;return r}function mt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function ht(){var e=ct;return 0===(4194240&(ct<<=1))&&(ct=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-it(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var bt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var jt,St,Nt,wt,kt,zt=!1,Ct=[],Et=null,_t=null,Tt=null,Pt=new Map,Ot=new Map,Rt=[],It="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Lt(e,t){switch(e){case"focusin":case"focusout":Et=null;break;case"dragenter":case"dragleave":_t=null;break;case"mouseover":case"mouseout":Tt=null;break;case"pointerover":case"pointerout":Pt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ot.delete(t.pointerId)}}function Dt(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=ba(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Mt(e){var t=ya(e.target);if(null!==t){var n=We(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Ve(n)))return e.blockedOn=t,void kt(e.priority,(function(){Nt(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Ft(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Jt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ba(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function At(e,t,n){Ft(e)&&n.delete(t)}function Ut(){zt=!1,null!==Et&&Ft(Et)&&(Et=null),null!==_t&&Ft(_t)&&(_t=null),null!==Tt&&Ft(Tt)&&(Tt=null),Pt.forEach(At),Ot.forEach(At)}function Bt(e,t){e.blockedOn===t&&(e.blockedOn=null,zt||(zt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Ut)))}function Wt(e){function t(t){return Bt(t,e)}if(0<Ct.length){Bt(Ct[0],e);for(var n=1;n<Ct.length;n++){var r=Ct[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Et&&Bt(Et,e),null!==_t&&Bt(_t,e),null!==Tt&&Bt(Tt,e),Pt.forEach(t),Ot.forEach(t),n=0;n<Rt.length;n++)(r=Rt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Rt.length&&null===(n=Rt[0]).blockedOn;)Mt(n),null===n.blockedOn&&Rt.shift()}var Vt=x.ReactCurrentBatchConfig,$t=!0;function Ht(e,t,n,r){var a=bt,l=Vt.transition;Vt.transition=null;try{bt=1,qt(e,t,n,r)}finally{bt=a,Vt.transition=l}}function Qt(e,t,n,r){var a=bt,l=Vt.transition;Vt.transition=null;try{bt=4,qt(e,t,n,r)}finally{bt=a,Vt.transition=l}}function qt(e,t,n,r){if($t){var a=Jt(e,t,n,r);if(null===a)$r(e,t,r,Kt,n),Lt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Et=Dt(Et,e,t,n,r,a),!0;case"dragenter":return _t=Dt(_t,e,t,n,r,a),!0;case"mouseover":return Tt=Dt(Tt,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return Pt.set(l,Dt(Pt.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,Ot.set(l,Dt(Ot.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Lt(e,r),4&t&&-1<It.indexOf(e)){for(;null!==a;){var l=ba(a);if(null!==l&&jt(l),null===(l=Jt(e,t,n,r))&&$r(e,t,r,Kt,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else $r(e,t,r,null,n)}}var Kt=null;function Jt(e,t,n,r){if(Kt=null,null!==(e=ya(e=je(r))))if(null===(t=We(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Ve(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Yt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ze()){case Xe:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Gt=null,Zt=null,Xt=null;function en(){if(Xt)return Xt;var e,t,n=Zt,r=n.length,a="value"in Gt?Gt.value:Gt.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===a[l-t];t++);return Xt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,l){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(a):a[i]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return M(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var ln,on,sn,cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},un=an(cn),dn=M({},cn,{view:0,detail:0}),fn=an(dn),mn=M({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:kn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(ln=e.screenX-sn.screenX,on=e.screenY-sn.screenY):on=ln=0,sn=e),ln)},movementY:function(e){return"movementY"in e?e.movementY:on}}),pn=an(mn),hn=an(M({},mn,{dataTransfer:0})),gn=an(M({},dn,{relatedTarget:0})),vn=an(M({},cn,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=M({},cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(yn),xn=an(M({},cn,{data:0})),jn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Nn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function wn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Nn[e])&&!!t[e]}function kn(){return wn}var zn=M({},dn,{key:function(e){if(e.key){var t=jn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:kn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Cn=an(zn),En=an(M({},mn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),_n=an(M({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:kn})),Tn=an(M({},cn,{propertyName:0,elapsedTime:0,pseudoElement:0})),Pn=M({},mn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),On=an(Pn),Rn=[9,13,27,32],In=u&&"CompositionEvent"in window,Ln=null;u&&"documentMode"in document&&(Ln=document.documentMode);var Dn=u&&"TextEvent"in window&&!Ln,Mn=u&&(!In||Ln&&8<Ln&&11>=Ln),Fn=String.fromCharCode(32),An=!1;function Un(e,t){switch(e){case"keyup":return-1!==Rn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Wn=!1;var Vn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function $n(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Vn[e.type]:"textarea"===t}function Hn(e,t,n,r){ze(r),0<(t=Qr(t,"onChange")).length&&(n=new un("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Qn=null,qn=null;function Kn(e){Fr(e,0)}function Jn(e){if(q(xa(e)))return e}function Yn(e,t){if("change"===e)return t}var Gn=!1;if(u){var Zn;if(u){var Xn="oninput"in document;if(!Xn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Xn="function"===typeof er.oninput}Zn=Xn}else Zn=!1;Gn=Zn&&(!document.documentMode||9<document.documentMode)}function tr(){Qn&&(Qn.detachEvent("onpropertychange",nr),qn=Qn=null)}function nr(e){if("value"===e.propertyName&&Jn(qn)){var t=[];Hn(t,qn,e,je(e)),Pe(Kn,t)}}function rr(e,t,n){"focusin"===e?(tr(),qn=n,(Qn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Jn(qn)}function lr(e,t){if("click"===e)return Jn(t)}function ir(e,t){if("input"===e||"change"===e)return Jn(t)}var or="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function sr(e,t){if(or(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!or(e[a],t[a]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ur(e,t){var n,r=cr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cr(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function mr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function pr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&mr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,l=Math.min(r.start,a);r=void 0===r.end?l:Math.min(r.end,a),!e.extend&&l>r&&(a=r,r=l,l=a),a=ur(n,l);var i=ur(n,r);a&&i&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var hr=u&&"documentMode"in document&&11>=document.documentMode,gr=null,vr=null,yr=null,br=!1;function xr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==gr||gr!==K(r)||("selectionStart"in(r=gr)&&mr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&sr(yr,r)||(yr=r,0<(r=Qr(vr,"onSelect")).length&&(t=new un("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function jr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:jr("Animation","AnimationEnd"),animationiteration:jr("Animation","AnimationIteration"),animationstart:jr("Animation","AnimationStart"),transitionend:jr("Transition","TransitionEnd")},Nr={},wr={};function kr(e){if(Nr[e])return Nr[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in wr)return Nr[e]=n[t];return e}u&&(wr=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var zr=kr("animationend"),Cr=kr("animationiteration"),Er=kr("animationstart"),_r=kr("transitionend"),Tr=new Map,Pr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Or(e,t){Tr.set(e,t),s(t,[e])}for(var Rr=0;Rr<Pr.length;Rr++){var Ir=Pr[Rr];Or(Ir.toLowerCase(),"on"+(Ir[0].toUpperCase()+Ir.slice(1)))}Or(zr,"onAnimationEnd"),Or(Cr,"onAnimationIteration"),Or(Er,"onAnimationStart"),Or("dblclick","onDoubleClick"),Or("focusin","onFocus"),Or("focusout","onBlur"),Or(_r,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Lr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Dr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Lr));function Mr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,i,o,s,c){if(Be.apply(this,arguments),De){if(!De)throw Error(l(198));var u=Me;De=!1,Me=null,Fe||(Fe=!0,Ae=u)}}(r,t,void 0,e),e.currentTarget=null}function Fr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var i=r.length-1;0<=i;i--){var o=r[i],s=o.instance,c=o.currentTarget;if(o=o.listener,s!==l&&a.isPropagationStopped())break e;Mr(a,o,c),l=s}else for(i=0;i<r.length;i++){if(s=(o=r[i]).instance,c=o.currentTarget,o=o.listener,s!==l&&a.isPropagationStopped())break e;Mr(a,o,c),l=s}}}if(Fe)throw e=Ae,Fe=!1,Ae=null,e}function Ar(e,t){var n=t[ha];void 0===n&&(n=t[ha]=new Set);var r=e+"__bubble";n.has(r)||(Vr(t,e,2,!1),n.add(r))}function Ur(e,t,n){var r=0;t&&(r|=4),Vr(n,e,r,t)}var Br="_reactListening"+Math.random().toString(36).slice(2);function Wr(e){if(!e[Br]){e[Br]=!0,i.forEach((function(t){"selectionchange"!==t&&(Dr.has(t)||Ur(t,!1,e),Ur(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Br]||(t[Br]=!0,Ur("selectionchange",!1,t))}}function Vr(e,t,n,r){switch(Yt(t)){case 1:var a=Ht;break;case 4:a=Qt;break;default:a=qt}n=a.bind(null,t,n,e),a=void 0,!Re||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function $r(e,t,n,r,a){var l=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var o=r.stateNode.containerInfo;if(o===a||8===o.nodeType&&o.parentNode===a)break;if(4===i)for(i=r.return;null!==i;){var s=i.tag;if((3===s||4===s)&&((s=i.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;i=i.return}for(;null!==o;){if(null===(i=ya(o)))return;if(5===(s=i.tag)||6===s){r=l=i;continue e}o=o.parentNode}}r=r.return}Pe((function(){var r=l,a=je(n),i=[];e:{var o=Tr.get(e);if(void 0!==o){var s=un,c=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=Cn;break;case"focusin":c="focus",s=gn;break;case"focusout":c="blur",s=gn;break;case"beforeblur":case"afterblur":s=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=pn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=hn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=_n;break;case zr:case Cr:case Er:s=vn;break;case _r:s=Tn;break;case"scroll":s=fn;break;case"wheel":s=On;break;case"copy":case"cut":case"paste":s=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=En}var u=0!==(4&t),d=!u&&"scroll"===e,f=u?null!==o?o+"Capture":null:o;u=[];for(var m,p=r;null!==p;){var h=(m=p).stateNode;if(5===m.tag&&null!==h&&(m=h,null!==f&&(null!=(h=Oe(p,f))&&u.push(Hr(p,h,m)))),d)break;p=p.return}0<u.length&&(o=new s(o,c,null,n,a),i.push({event:o,listeners:u}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(o="mouseover"===e||"pointerover"===e)||n===xe||!(c=n.relatedTarget||n.fromElement)||!ya(c)&&!c[pa])&&(s||o)&&(o=a.window===a?a:(o=a.ownerDocument)?o.defaultView||o.parentWindow:window,s?(s=r,null!==(c=(c=n.relatedTarget||n.toElement)?ya(c):null)&&(c!==(d=We(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(s=null,c=r),s!==c)){if(u=pn,h="onMouseLeave",f="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(u=En,h="onPointerLeave",f="onPointerEnter",p="pointer"),d=null==s?o:xa(s),m=null==c?o:xa(c),(o=new u(h,p+"leave",s,n,a)).target=d,o.relatedTarget=m,h=null,ya(a)===r&&((u=new u(f,p+"enter",c,n,a)).target=m,u.relatedTarget=d,h=u),d=h,s&&c)e:{for(f=c,p=0,m=u=s;m;m=qr(m))p++;for(m=0,h=f;h;h=qr(h))m++;for(;0<p-m;)u=qr(u),p--;for(;0<m-p;)f=qr(f),m--;for(;p--;){if(u===f||null!==f&&u===f.alternate)break e;u=qr(u),f=qr(f)}u=null}else u=null;null!==s&&Kr(i,o,s,u,!1),null!==c&&null!==d&&Kr(i,d,c,u,!0)}if("select"===(s=(o=r?xa(r):window).nodeName&&o.nodeName.toLowerCase())||"input"===s&&"file"===o.type)var g=Yn;else if($n(o))if(Gn)g=ir;else{g=ar;var v=rr}else(s=o.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===o.type||"radio"===o.type)&&(g=lr);switch(g&&(g=g(e,r))?Hn(i,g,n,a):(v&&v(e,o,r),"focusout"===e&&(v=o._wrapperState)&&v.controlled&&"number"===o.type&&ee(o,"number",o.value)),v=r?xa(r):window,e){case"focusin":($n(v)||"true"===v.contentEditable)&&(gr=v,vr=r,yr=null);break;case"focusout":yr=vr=gr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,xr(i,n,a);break;case"selectionchange":if(hr)break;case"keydown":case"keyup":xr(i,n,a)}var y;if(In)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Wn?Un(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Mn&&"ko"!==n.locale&&(Wn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Wn&&(y=en()):(Zt="value"in(Gt=a)?Gt.value:Gt.textContent,Wn=!0)),0<(v=Qr(r,b)).length&&(b=new xn(b,e,null,n,a),i.push({event:b,listeners:v}),y?b.data=y:null!==(y=Bn(n))&&(b.data=y))),(y=Dn?function(e,t){switch(e){case"compositionend":return Bn(t);case"keypress":return 32!==t.which?null:(An=!0,Fn);case"textInput":return(e=t.data)===Fn&&An?null:e;default:return null}}(e,n):function(e,t){if(Wn)return"compositionend"===e||!In&&Un(e,t)?(e=en(),Xt=Zt=Gt=null,Wn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Mn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Qr(r,"onBeforeInput")).length&&(a=new xn("onBeforeInput","beforeinput",null,n,a),i.push({event:a,listeners:r}),a.data=y))}Fr(i,t)}))}function Hr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Qr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;5===a.tag&&null!==l&&(a=l,null!=(l=Oe(e,n))&&r.unshift(Hr(e,l,a)),null!=(l=Oe(e,t))&&r.push(Hr(e,l,a))),e=e.return}return r}function qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,a){for(var l=t._reactName,i=[];null!==n&&n!==r;){var o=n,s=o.alternate,c=o.stateNode;if(null!==s&&s===r)break;5===o.tag&&null!==c&&(o=c,a?null!=(s=Oe(n,l))&&i.unshift(Hr(n,s,o)):a||null!=(s=Oe(n,l))&&i.push(Hr(n,s,o))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Jr=/\r\n?/g,Yr=/\u0000|\uFFFD/g;function Gr(e){return("string"===typeof e?e:""+e).replace(Jr,"\n").replace(Yr,"")}function Zr(e,t,n){if(t=Gr(t),Gr(e)!==t&&n)throw Error(l(425))}function Xr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,la="function"===typeof Promise?Promise:void 0,ia="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof la?function(e){return la.resolve(null).then(e).catch(oa)}:ra;function oa(e){setTimeout((function(){throw e}))}function sa(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Wt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Wt(t)}function ca(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ua(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,ma="__reactProps$"+da,pa="__reactContainer$"+da,ha="__reactEvents$"+da,ga="__reactListeners$"+da,va="__reactHandles$"+da;function ya(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pa]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ua(e);null!==e;){if(n=e[fa])return n;e=ua(e)}return t}n=(e=n).parentNode}return null}function ba(e){return!(e=e[fa]||e[pa])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(l(33))}function ja(e){return e[ma]||null}var Sa=[],Na=-1;function wa(e){return{current:e}}function ka(e){0>Na||(e.current=Sa[Na],Sa[Na]=null,Na--)}function za(e,t){Na++,Sa[Na]=e.current,e.current=t}var Ca={},Ea=wa(Ca),_a=wa(!1),Ta=Ca;function Pa(e,t){var n=e.type.contextTypes;if(!n)return Ca;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,l={};for(a in n)l[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Oa(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Ra(){ka(_a),ka(Ea)}function Ia(e,t,n){if(Ea.current!==Ca)throw Error(l(168));za(Ea,t),za(_a,n)}function La(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(l(108,V(e)||"Unknown",a));return M({},n,r)}function Da(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ca,Ta=Ea.current,za(Ea,e),za(_a,_a.current),!0}function Ma(e,t,n){var r=e.stateNode;if(!r)throw Error(l(169));n?(e=La(e,t,Ta),r.__reactInternalMemoizedMergedChildContext=e,ka(_a),ka(Ea),za(Ea,e)):ka(_a),za(_a,n)}var Fa=null,Aa=!1,Ua=!1;function Ba(e){null===Fa?Fa=[e]:Fa.push(e)}function Wa(){if(!Ua&&null!==Fa){Ua=!0;var e=0,t=bt;try{var n=Fa;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Fa=null,Aa=!1}catch(a){throw null!==Fa&&(Fa=Fa.slice(e+1)),qe(Xe,Wa),a}finally{bt=t,Ua=!1}}return null}var Va=[],$a=0,Ha=null,Qa=0,qa=[],Ka=0,Ja=null,Ya=1,Ga="";function Za(e,t){Va[$a++]=Qa,Va[$a++]=Ha,Ha=e,Qa=t}function Xa(e,t,n){qa[Ka++]=Ya,qa[Ka++]=Ga,qa[Ka++]=Ja,Ja=e;var r=Ya;e=Ga;var a=32-it(r)-1;r&=~(1<<a),n+=1;var l=32-it(t)+a;if(30<l){var i=a-a%5;l=(r&(1<<i)-1).toString(32),r>>=i,a-=i,Ya=1<<32-it(t)+a|n<<a|r,Ga=l+e}else Ya=1<<l|n<<a|r,Ga=e}function el(e){null!==e.return&&(Za(e,1),Xa(e,1,0))}function tl(e){for(;e===Ha;)Ha=Va[--$a],Va[$a]=null,Qa=Va[--$a],Va[$a]=null;for(;e===Ja;)Ja=qa[--Ka],qa[Ka]=null,Ga=qa[--Ka],qa[Ka]=null,Ya=qa[--Ka],qa[Ka]=null}var nl=null,rl=null,al=!1,ll=null;function il(e,t){var n=Pc(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function ol(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,nl=e,rl=ca(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,nl=e,rl=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ja?{id:Ya,overflow:Ga}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Pc(18,null,null,0)).stateNode=t,n.return=e,e.child=n,nl=e,rl=null,!0);default:return!1}}function sl(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function cl(e){if(al){var t=rl;if(t){var n=t;if(!ol(e,t)){if(sl(e))throw Error(l(418));t=ca(n.nextSibling);var r=nl;t&&ol(e,t)?il(r,n):(e.flags=-4097&e.flags|2,al=!1,nl=e)}}else{if(sl(e))throw Error(l(418));e.flags=-4097&e.flags|2,al=!1,nl=e}}}function ul(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;nl=e}function dl(e){if(e!==nl)return!1;if(!al)return ul(e),al=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=rl)){if(sl(e))throw fl(),Error(l(418));for(;t;)il(e,t),t=ca(t.nextSibling)}if(ul(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){rl=ca(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}rl=null}}else rl=nl?ca(e.stateNode.nextSibling):null;return!0}function fl(){for(var e=rl;e;)e=ca(e.nextSibling)}function ml(){rl=nl=null,al=!1}function pl(e){null===ll?ll=[e]:ll.push(e)}var hl=x.ReactCurrentBatchConfig;function gl(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(l(309));var r=n.stateNode}if(!r)throw Error(l(147,e));var a=r,i=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=a.refs;null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!==typeof e)throw Error(l(284));if(!n._owner)throw Error(l(290,e))}return e}function vl(e,t){throw e=Object.prototype.toString.call(t),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yl(e){return(0,e._init)(e._payload)}function bl(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Rc(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function o(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Mc(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function c(e,t,n,r){var l=n.type;return l===N?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"===typeof l&&null!==l&&l.$$typeof===O&&yl(l)===t.type)?((r=a(t,n.props)).ref=gl(e,t,n),r.return=e,r):((r=Ic(n.type,n.key,n.props,null,e.mode,r)).ref=gl(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Fc(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=Lc(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Mc(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case j:return(n=Ic(t.type,t.key,t.props,null,e.mode,n)).ref=gl(e,null,t),n.return=e,n;case S:return(t=Fc(t,e.mode,n)).return=e,t;case O:return f(e,(0,t._init)(t._payload),n)}if(te(t)||L(t))return(t=Lc(t,e.mode,n,null)).return=e,t;vl(e,t)}return null}function m(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case j:return n.key===a?c(e,t,n,r):null;case S:return n.key===a?u(e,t,n,r):null;case O:return m(e,t,(a=n._init)(n._payload),r)}if(te(n)||L(n))return null!==a?null:d(e,t,n,r,null);vl(e,n)}return null}function p(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case j:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case S:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case O:return p(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||L(r))return d(t,e=e.get(n)||null,r,a,null);vl(t,r)}return null}function h(a,l,o,s){for(var c=null,u=null,d=l,h=l=0,g=null;null!==d&&h<o.length;h++){d.index>h?(g=d,d=null):g=d.sibling;var v=m(a,d,o[h],s);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(a,d),l=i(v,l,h),null===u?c=v:u.sibling=v,u=v,d=g}if(h===o.length)return n(a,d),al&&Za(a,h),c;if(null===d){for(;h<o.length;h++)null!==(d=f(a,o[h],s))&&(l=i(d,l,h),null===u?c=d:u.sibling=d,u=d);return al&&Za(a,h),c}for(d=r(a,d);h<o.length;h++)null!==(g=p(d,a,h,o[h],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?h:g.key),l=i(g,l,h),null===u?c=g:u.sibling=g,u=g);return e&&d.forEach((function(e){return t(a,e)})),al&&Za(a,h),c}function g(a,o,s,c){var u=L(s);if("function"!==typeof u)throw Error(l(150));if(null==(s=u.call(s)))throw Error(l(151));for(var d=u=null,h=o,g=o=0,v=null,y=s.next();null!==h&&!y.done;g++,y=s.next()){h.index>g?(v=h,h=null):v=h.sibling;var b=m(a,h,y.value,c);if(null===b){null===h&&(h=v);break}e&&h&&null===b.alternate&&t(a,h),o=i(b,o,g),null===d?u=b:d.sibling=b,d=b,h=v}if(y.done)return n(a,h),al&&Za(a,g),u;if(null===h){for(;!y.done;g++,y=s.next())null!==(y=f(a,y.value,c))&&(o=i(y,o,g),null===d?u=y:d.sibling=y,d=y);return al&&Za(a,g),u}for(h=r(a,h);!y.done;g++,y=s.next())null!==(y=p(h,a,g,y.value,c))&&(e&&null!==y.alternate&&h.delete(null===y.key?g:y.key),o=i(y,o,g),null===d?u=y:d.sibling=y,d=y);return e&&h.forEach((function(e){return t(a,e)})),al&&Za(a,g),u}return function e(r,l,i,s){if("object"===typeof i&&null!==i&&i.type===N&&null===i.key&&(i=i.props.children),"object"===typeof i&&null!==i){switch(i.$$typeof){case j:e:{for(var c=i.key,u=l;null!==u;){if(u.key===c){if((c=i.type)===N){if(7===u.tag){n(r,u.sibling),(l=a(u,i.props.children)).return=r,r=l;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===O&&yl(c)===u.type){n(r,u.sibling),(l=a(u,i.props)).ref=gl(r,u,i),l.return=r,r=l;break e}n(r,u);break}t(r,u),u=u.sibling}i.type===N?((l=Lc(i.props.children,r.mode,s,i.key)).return=r,r=l):((s=Ic(i.type,i.key,i.props,null,r.mode,s)).ref=gl(r,l,i),s.return=r,r=s)}return o(r);case S:e:{for(u=i.key;null!==l;){if(l.key===u){if(4===l.tag&&l.stateNode.containerInfo===i.containerInfo&&l.stateNode.implementation===i.implementation){n(r,l.sibling),(l=a(l,i.children||[])).return=r,r=l;break e}n(r,l);break}t(r,l),l=l.sibling}(l=Fc(i,r.mode,s)).return=r,r=l}return o(r);case O:return e(r,l,(u=i._init)(i._payload),s)}if(te(i))return h(r,l,i,s);if(L(i))return g(r,l,i,s);vl(r,i)}return"string"===typeof i&&""!==i||"number"===typeof i?(i=""+i,null!==l&&6===l.tag?(n(r,l.sibling),(l=a(l,i)).return=r,r=l):(n(r,l),(l=Mc(i,r.mode,s)).return=r,r=l),o(r)):n(r,l)}}var xl=bl(!0),jl=bl(!1),Sl=wa(null),Nl=null,wl=null,kl=null;function zl(){kl=wl=Nl=null}function Cl(e){var t=Sl.current;ka(Sl),e._currentValue=t}function El(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function _l(e,t){Nl=e,kl=wl=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bo=!0),e.firstContext=null)}function Tl(e){var t=e._currentValue;if(kl!==e)if(e={context:e,memoizedValue:t,next:null},null===wl){if(null===Nl)throw Error(l(308));wl=e,Nl.dependencies={lanes:0,firstContext:e}}else wl=wl.next=e;return t}var Pl=null;function Ol(e){null===Pl?Pl=[e]:Pl.push(e)}function Rl(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Ol(t)):(n.next=a.next,a.next=n),t.interleaved=n,Il(e,r)}function Il(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Ll=!1;function Dl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ml(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Fl(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Al(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Es)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Il(e,n)}return null===(a=r.interleaved)?(t.next=t,Ol(r)):(t.next=a.next,a.next=t),r.interleaved=t,Il(e,n)}function Ul(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Bl(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===l?a=l=i:l=l.next=i,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Wl(e,t,n,r){var a=e.updateQueue;Ll=!1;var l=a.firstBaseUpdate,i=a.lastBaseUpdate,o=a.shared.pending;if(null!==o){a.shared.pending=null;var s=o,c=s.next;s.next=null,null===i?l=c:i.next=c,i=s;var u=e.alternate;null!==u&&((o=(u=u.updateQueue).lastBaseUpdate)!==i&&(null===o?u.firstBaseUpdate=c:o.next=c,u.lastBaseUpdate=s))}if(null!==l){var d=a.baseState;for(i=0,u=c=s=null,o=l;;){var f=o.lane,m=o.eventTime;if((r&f)===f){null!==u&&(u=u.next={eventTime:m,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var p=e,h=o;switch(f=t,m=n,h.tag){case 1:if("function"===typeof(p=h.payload)){d=p.call(m,d,f);break e}d=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null===(f="function"===typeof(p=h.payload)?p.call(m,d,f):p)||void 0===f)break e;d=M({},d,f);break e;case 2:Ll=!0}}null!==o.callback&&0!==o.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[o]:f.push(o))}else m={eventTime:m,lane:f,tag:o.tag,payload:o.payload,callback:o.callback,next:null},null===u?(c=u=m,s=d):u=u.next=m,i|=f;if(null===(o=o.next)){if(null===(o=a.shared.pending))break;o=(f=o).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===u&&(s=d),a.baseState=s,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null!==(t=a.shared.interleaved)){a=t;do{i|=a.lane,a=a.next}while(a!==t)}else null===l&&(a.shared.lanes=0);Ds|=i,e.lanes=i,e.memoizedState=d}}function Vl(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(l(191,a));a.call(r)}}}var $l={},Hl=wa($l),Ql=wa($l),ql=wa($l);function Kl(e){if(e===$l)throw Error(l(174));return e}function Jl(e,t){switch(za(ql,t),za(Ql,e),za(Hl,$l),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}ka(Hl),za(Hl,t)}function Yl(){ka(Hl),ka(Ql),ka(ql)}function Gl(e){Kl(ql.current);var t=Kl(Hl.current),n=se(t,e.type);t!==n&&(za(Ql,e),za(Hl,n))}function Zl(e){Ql.current===e&&(ka(Hl),ka(Ql))}var Xl=wa(0);function ei(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ti=[];function ni(){for(var e=0;e<ti.length;e++)ti[e]._workInProgressVersionPrimary=null;ti.length=0}var ri=x.ReactCurrentDispatcher,ai=x.ReactCurrentBatchConfig,li=0,ii=null,oi=null,si=null,ci=!1,ui=!1,di=0,fi=0;function mi(){throw Error(l(321))}function pi(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!or(e[n],t[n]))return!1;return!0}function hi(e,t,n,r,a,i){if(li=i,ii=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ri.current=null===e||null===e.memoizedState?Zi:Xi,e=n(r,a),ui){i=0;do{if(ui=!1,di=0,25<=i)throw Error(l(301));i+=1,si=oi=null,t.updateQueue=null,ri.current=eo,e=n(r,a)}while(ui)}if(ri.current=Gi,t=null!==oi&&null!==oi.next,li=0,si=oi=ii=null,ci=!1,t)throw Error(l(300));return e}function gi(){var e=0!==di;return di=0,e}function vi(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===si?ii.memoizedState=si=e:si=si.next=e,si}function yi(){if(null===oi){var e=ii.alternate;e=null!==e?e.memoizedState:null}else e=oi.next;var t=null===si?ii.memoizedState:si.next;if(null!==t)si=t,oi=e;else{if(null===e)throw Error(l(310));e={memoizedState:(oi=e).memoizedState,baseState:oi.baseState,baseQueue:oi.baseQueue,queue:oi.queue,next:null},null===si?ii.memoizedState=si=e:si=si.next=e}return si}function bi(e,t){return"function"===typeof t?t(e):t}function xi(e){var t=yi(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=oi,a=r.baseQueue,i=n.pending;if(null!==i){if(null!==a){var o=a.next;a.next=i.next,i.next=o}r.baseQueue=a=i,n.pending=null}if(null!==a){i=a.next,r=r.baseState;var s=o=null,c=null,u=i;do{var d=u.lane;if((li&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(s=c=f,o=r):c=c.next=f,ii.lanes|=d,Ds|=d}u=u.next}while(null!==u&&u!==i);null===c?o=r:c.next=s,or(r,t.memoizedState)||(bo=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=c,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{i=a.lane,ii.lanes|=i,Ds|=i,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ji(e){var t=yi(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,i=t.memoizedState;if(null!==a){n.pending=null;var o=a=a.next;do{i=e(i,o.action),o=o.next}while(o!==a);or(i,t.memoizedState)||(bo=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Si(){}function Ni(e,t){var n=ii,r=yi(),a=t(),i=!or(r.memoizedState,a);if(i&&(r.memoizedState=a,bo=!0),r=r.queue,Li(zi.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==si&&1&si.memoizedState.tag){if(n.flags|=2048,Ti(9,ki.bind(null,n,r,a,t),void 0,null),null===_s)throw Error(l(349));0!==(30&li)||wi(n,t,a)}return a}function wi(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ii.updateQueue)?(t={lastEffect:null,stores:null},ii.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function ki(e,t,n,r){t.value=n,t.getSnapshot=r,Ci(t)&&Ei(e)}function zi(e,t,n){return n((function(){Ci(t)&&Ei(e)}))}function Ci(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!or(e,n)}catch(r){return!0}}function Ei(e){var t=Il(e,1);null!==t&&nc(t,e,1,-1)}function _i(e){var t=vi();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:bi,lastRenderedState:e},t.queue=e,e=e.dispatch=qi.bind(null,ii,e),[t.memoizedState,e]}function Ti(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=ii.updateQueue)?(t={lastEffect:null,stores:null},ii.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Pi(){return yi().memoizedState}function Oi(e,t,n,r){var a=vi();ii.flags|=e,a.memoizedState=Ti(1|t,n,void 0,void 0===r?null:r)}function Ri(e,t,n,r){var a=yi();r=void 0===r?null:r;var l=void 0;if(null!==oi){var i=oi.memoizedState;if(l=i.destroy,null!==r&&pi(r,i.deps))return void(a.memoizedState=Ti(t,n,l,r))}ii.flags|=e,a.memoizedState=Ti(1|t,n,l,r)}function Ii(e,t){return Oi(8390656,8,e,t)}function Li(e,t){return Ri(2048,8,e,t)}function Di(e,t){return Ri(4,2,e,t)}function Mi(e,t){return Ri(4,4,e,t)}function Fi(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ai(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ri(4,4,Fi.bind(null,t,e),n)}function Ui(){}function Bi(e,t){var n=yi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&pi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Wi(e,t){var n=yi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&pi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Vi(e,t,n){return 0===(21&li)?(e.baseState&&(e.baseState=!1,bo=!0),e.memoizedState=n):(or(n,t)||(n=ht(),ii.lanes|=n,Ds|=n,e.baseState=!0),t)}function $i(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=ai.transition;ai.transition={};try{e(!1),t()}finally{bt=n,ai.transition=r}}function Hi(){return yi().memoizedState}function Qi(e,t,n){var r=tc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ki(e))Ji(t,n);else if(null!==(n=Rl(e,t,n,r))){nc(n,e,r,ec()),Yi(n,t,r)}}function qi(e,t,n){var r=tc(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ki(e))Ji(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var i=t.lastRenderedState,o=l(i,n);if(a.hasEagerState=!0,a.eagerState=o,or(o,i)){var s=t.interleaved;return null===s?(a.next=a,Ol(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(c){}null!==(n=Rl(e,t,a,r))&&(nc(n,e,r,a=ec()),Yi(n,t,r))}}function Ki(e){var t=e.alternate;return e===ii||null!==t&&t===ii}function Ji(e,t){ui=ci=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Yi(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var Gi={readContext:Tl,useCallback:mi,useContext:mi,useEffect:mi,useImperativeHandle:mi,useInsertionEffect:mi,useLayoutEffect:mi,useMemo:mi,useReducer:mi,useRef:mi,useState:mi,useDebugValue:mi,useDeferredValue:mi,useTransition:mi,useMutableSource:mi,useSyncExternalStore:mi,useId:mi,unstable_isNewReconciler:!1},Zi={readContext:Tl,useCallback:function(e,t){return vi().memoizedState=[e,void 0===t?null:t],e},useContext:Tl,useEffect:Ii,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Oi(4194308,4,Fi.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Oi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Oi(4,2,e,t)},useMemo:function(e,t){var n=vi();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=vi();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Qi.bind(null,ii,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},vi().memoizedState=e},useState:_i,useDebugValue:Ui,useDeferredValue:function(e){return vi().memoizedState=e},useTransition:function(){var e=_i(!1),t=e[0];return e=$i.bind(null,e[1]),vi().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ii,a=vi();if(al){if(void 0===n)throw Error(l(407));n=n()}else{if(n=t(),null===_s)throw Error(l(349));0!==(30&li)||wi(r,t,n)}a.memoizedState=n;var i={value:n,getSnapshot:t};return a.queue=i,Ii(zi.bind(null,r,i,e),[e]),r.flags|=2048,Ti(9,ki.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=vi(),t=_s.identifierPrefix;if(al){var n=Ga;t=":"+t+"R"+(n=(Ya&~(1<<32-it(Ya)-1)).toString(32)+n),0<(n=di++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=fi++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Xi={readContext:Tl,useCallback:Bi,useContext:Tl,useEffect:Li,useImperativeHandle:Ai,useInsertionEffect:Di,useLayoutEffect:Mi,useMemo:Wi,useReducer:xi,useRef:Pi,useState:function(){return xi(bi)},useDebugValue:Ui,useDeferredValue:function(e){return Vi(yi(),oi.memoizedState,e)},useTransition:function(){return[xi(bi)[0],yi().memoizedState]},useMutableSource:Si,useSyncExternalStore:Ni,useId:Hi,unstable_isNewReconciler:!1},eo={readContext:Tl,useCallback:Bi,useContext:Tl,useEffect:Li,useImperativeHandle:Ai,useInsertionEffect:Di,useLayoutEffect:Mi,useMemo:Wi,useReducer:ji,useRef:Pi,useState:function(){return ji(bi)},useDebugValue:Ui,useDeferredValue:function(e){var t=yi();return null===oi?t.memoizedState=e:Vi(t,oi.memoizedState,e)},useTransition:function(){return[ji(bi)[0],yi().memoizedState]},useMutableSource:Si,useSyncExternalStore:Ni,useId:Hi,unstable_isNewReconciler:!1};function to(e,t){if(e&&e.defaultProps){for(var n in t=M({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function no(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:M({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ro={isMounted:function(e){return!!(e=e._reactInternals)&&We(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ec(),a=tc(e),l=Fl(r,a);l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Al(e,l,a))&&(nc(t,e,a,r),Ul(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ec(),a=tc(e),l=Fl(r,a);l.tag=1,l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Al(e,l,a))&&(nc(t,e,a,r),Ul(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ec(),r=tc(e),a=Fl(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Al(e,a,r))&&(nc(t,e,r,n),Ul(t,e,r))}};function ao(e,t,n,r,a,l,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,i):!t.prototype||!t.prototype.isPureReactComponent||(!sr(n,r)||!sr(a,l))}function lo(e,t,n){var r=!1,a=Ca,l=t.contextType;return"object"===typeof l&&null!==l?l=Tl(l):(a=Oa(t)?Ta:Ea.current,l=(r=null!==(r=t.contextTypes)&&void 0!==r)?Pa(e,a):Ca),t=new t(n,l),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ro,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=l),t}function io(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ro.enqueueReplaceState(t,t.state,null)}function oo(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Dl(e);var l=t.contextType;"object"===typeof l&&null!==l?a.context=Tl(l):(l=Oa(t)?Ta:Ea.current,a.context=Pa(e,l)),a.state=e.memoizedState,"function"===typeof(l=t.getDerivedStateFromProps)&&(no(e,t,l,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&ro.enqueueReplaceState(a,a.state,null),Wl(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function so(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var a=n}catch(l){a="\nError generating stack: "+l.message+"\n"+l.stack}return{value:e,source:t,stack:a,digest:null}}function co(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function uo(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var fo="function"===typeof WeakMap?WeakMap:Map;function mo(e,t,n){(n=Fl(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){$s||($s=!0,Hs=r),uo(0,t)},n}function po(e,t,n){(n=Fl(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){uo(0,t)}}var l=e.stateNode;return null!==l&&"function"===typeof l.componentDidCatch&&(n.callback=function(){uo(0,t),"function"!==typeof r&&(null===Qs?Qs=new Set([this]):Qs.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ho(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fo;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=kc.bind(null,e,t,n),t.then(e,e))}function go(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function vo(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Fl(-1,1)).tag=2,Al(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var yo=x.ReactCurrentOwner,bo=!1;function xo(e,t,n,r){t.child=null===e?jl(t,null,n,r):xl(t,e.child,n,r)}function jo(e,t,n,r,a){n=n.render;var l=t.ref;return _l(t,a),r=hi(e,t,n,r,l,a),n=gi(),null===e||bo?(al&&n&&el(t),t.flags|=1,xo(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,$o(e,t,a))}function So(e,t,n,r,a){if(null===e){var l=n.type;return"function"!==typeof l||Oc(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Ic(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,No(e,t,l,r,a))}if(l=e.child,0===(e.lanes&a)){var i=l.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(i,r)&&e.ref===t.ref)return $o(e,t,a)}return t.flags|=1,(e=Rc(l,r)).ref=t.ref,e.return=t,t.child=e}function No(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(sr(l,r)&&e.ref===t.ref){if(bo=!1,t.pendingProps=r=l,0===(e.lanes&a))return t.lanes=e.lanes,$o(e,t,a);0!==(131072&e.flags)&&(bo=!0)}}return zo(e,t,n,r,a)}function wo(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},za(Rs,Os),Os|=n;else{if(0===(1073741824&n))return e=null!==l?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,za(Rs,Os),Os|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==l?l.baseLanes:n,za(Rs,Os),Os|=r}else null!==l?(r=l.baseLanes|n,t.memoizedState=null):r=n,za(Rs,Os),Os|=r;return xo(e,t,a,n),t.child}function ko(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function zo(e,t,n,r,a){var l=Oa(n)?Ta:Ea.current;return l=Pa(t,l),_l(t,a),n=hi(e,t,n,r,l,a),r=gi(),null===e||bo?(al&&r&&el(t),t.flags|=1,xo(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,$o(e,t,a))}function Co(e,t,n,r,a){if(Oa(n)){var l=!0;Da(t)}else l=!1;if(_l(t,a),null===t.stateNode)Vo(e,t),lo(t,n,r),oo(t,n,r,a),r=!0;else if(null===e){var i=t.stateNode,o=t.memoizedProps;i.props=o;var s=i.context,c=n.contextType;"object"===typeof c&&null!==c?c=Tl(c):c=Pa(t,c=Oa(n)?Ta:Ea.current);var u=n.getDerivedStateFromProps,d="function"===typeof u||"function"===typeof i.getSnapshotBeforeUpdate;d||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(o!==r||s!==c)&&io(t,i,r,c),Ll=!1;var f=t.memoizedState;i.state=f,Wl(t,r,i,a),s=t.memoizedState,o!==r||f!==s||_a.current||Ll?("function"===typeof u&&(no(t,n,u,r),s=t.memoizedState),(o=Ll||ao(t,n,o,r,f,s,c))?(d||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||("function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"===typeof i.componentDidMount&&(t.flags|=4194308)):("function"===typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),i.props=r,i.state=s,i.context=c,r=o):("function"===typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Ml(e,t),o=t.memoizedProps,c=t.type===t.elementType?o:to(t.type,o),i.props=c,d=t.pendingProps,f=i.context,"object"===typeof(s=n.contextType)&&null!==s?s=Tl(s):s=Pa(t,s=Oa(n)?Ta:Ea.current);var m=n.getDerivedStateFromProps;(u="function"===typeof m||"function"===typeof i.getSnapshotBeforeUpdate)||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(o!==d||f!==s)&&io(t,i,r,s),Ll=!1,f=t.memoizedState,i.state=f,Wl(t,r,i,a);var p=t.memoizedState;o!==d||f!==p||_a.current||Ll?("function"===typeof m&&(no(t,n,m,r),p=t.memoizedState),(c=Ll||ao(t,n,c,r,f,p,s)||!1)?(u||"function"!==typeof i.UNSAFE_componentWillUpdate&&"function"!==typeof i.componentWillUpdate||("function"===typeof i.componentWillUpdate&&i.componentWillUpdate(r,p,s),"function"===typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,p,s)),"function"===typeof i.componentDidUpdate&&(t.flags|=4),"function"===typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof i.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),i.props=r,i.state=p,i.context=s,r=c):("function"!==typeof i.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Eo(e,t,n,r,l,a)}function Eo(e,t,n,r,a,l){ko(e,t);var i=0!==(128&t.flags);if(!r&&!i)return a&&Ma(t,n,!1),$o(e,t,l);r=t.stateNode,yo.current=t;var o=i&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=xl(t,e.child,null,l),t.child=xl(t,null,o,l)):xo(e,t,o,l),t.memoizedState=r.state,a&&Ma(t,n,!0),t.child}function _o(e){var t=e.stateNode;t.pendingContext?Ia(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Ia(0,t.context,!1),Jl(e,t.containerInfo)}function To(e,t,n,r,a){return ml(),pl(a),t.flags|=256,xo(e,t,n,r),t.child}var Po,Oo,Ro,Io,Lo={dehydrated:null,treeContext:null,retryLane:0};function Do(e){return{baseLanes:e,cachePool:null,transitions:null}}function Mo(e,t,n){var r,a=t.pendingProps,i=Xl.current,o=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&i)),r?(o=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),za(Xl,1&i),null===e)return cl(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=a.children,e=a.fallback,o?(a=t.mode,o=t.child,s={mode:"hidden",children:s},0===(1&a)&&null!==o?(o.childLanes=0,o.pendingProps=s):o=Dc(s,a,0,null),e=Lc(e,a,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Do(n),t.memoizedState=Lo,e):Fo(t,s));if(null!==(i=e.memoizedState)&&null!==(r=i.dehydrated))return function(e,t,n,r,a,i,o){if(n)return 256&t.flags?(t.flags&=-257,Ao(e,t,o,r=co(Error(l(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=r.fallback,a=t.mode,r=Dc({mode:"visible",children:r.children},a,0,null),(i=Lc(i,a,o,null)).flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,0!==(1&t.mode)&&xl(t,e.child,null,o),t.child.memoizedState=Do(o),t.memoizedState=Lo,i);if(0===(1&t.mode))return Ao(e,t,o,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Ao(e,t,o,r=co(i=Error(l(419)),r,void 0))}if(s=0!==(o&e.childLanes),bo||s){if(null!==(r=_s)){switch(o&-o){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|o))?0:a)&&a!==i.retryLane&&(i.retryLane=a,Il(e,a),nc(r,e,a,-1))}return hc(),Ao(e,t,o,r=co(Error(l(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Cc.bind(null,e),a._reactRetry=t,null):(e=i.treeContext,rl=ca(a.nextSibling),nl=t,al=!0,ll=null,null!==e&&(qa[Ka++]=Ya,qa[Ka++]=Ga,qa[Ka++]=Ja,Ya=e.id,Ga=e.overflow,Ja=t),t=Fo(t,r.children),t.flags|=4096,t)}(e,t,s,a,r,i,n);if(o){o=a.fallback,s=t.mode,r=(i=e.child).sibling;var c={mode:"hidden",children:a.children};return 0===(1&s)&&t.child!==i?((a=t.child).childLanes=0,a.pendingProps=c,t.deletions=null):(a=Rc(i,c)).subtreeFlags=14680064&i.subtreeFlags,null!==r?o=Rc(r,o):(o=Lc(o,s,n,null)).flags|=2,o.return=t,a.return=t,a.sibling=o,t.child=a,a=o,o=t.child,s=null===(s=e.child.memoizedState)?Do(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=Lo,a}return e=(o=e.child).sibling,a=Rc(o,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Fo(e,t){return(t=Dc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Ao(e,t,n,r){return null!==r&&pl(r),xl(t,e.child,null,n),(e=Fo(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Uo(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),El(e.return,t,n)}function Bo(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Wo(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(xo(e,t,r.children,n),0!==(2&(r=Xl.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Uo(e,n,t);else if(19===e.tag)Uo(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(za(Xl,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===ei(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Bo(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===ei(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Bo(t,!0,n,null,l);break;case"together":Bo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Vo(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function $o(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Ds|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=Rc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Rc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Ho(e,t){if(!al)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Qo(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function qo(e,t,n){var r=t.pendingProps;switch(tl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Qo(t),null;case 1:case 17:return Oa(t.type)&&Ra(),Qo(t),null;case 3:return r=t.stateNode,Yl(),ka(_a),ka(Ea),ni(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(dl(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ll&&(ic(ll),ll=null))),Oo(e,t),Qo(t),null;case 5:Zl(t);var a=Kl(ql.current);if(n=t.type,null!==e&&null!=t.stateNode)Ro(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(l(166));return Qo(t),null}if(e=Kl(Hl.current),dl(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[fa]=t,r[ma]=i,e=0!==(1&t.mode),n){case"dialog":Ar("cancel",r),Ar("close",r);break;case"iframe":case"object":case"embed":Ar("load",r);break;case"video":case"audio":for(a=0;a<Lr.length;a++)Ar(Lr[a],r);break;case"source":Ar("error",r);break;case"img":case"image":case"link":Ar("error",r),Ar("load",r);break;case"details":Ar("toggle",r);break;case"input":Y(r,i),Ar("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Ar("invalid",r);break;case"textarea":ae(r,i),Ar("invalid",r)}for(var s in ye(n,i),a=null,i)if(i.hasOwnProperty(s)){var c=i[s];"children"===s?"string"===typeof c?r.textContent!==c&&(!0!==i.suppressHydrationWarning&&Zr(r.textContent,c,e),a=["children",c]):"number"===typeof c&&r.textContent!==""+c&&(!0!==i.suppressHydrationWarning&&Zr(r.textContent,c,e),a=["children",""+c]):o.hasOwnProperty(s)&&null!=c&&"onScroll"===s&&Ar("scroll",r)}switch(n){case"input":Q(r),X(r,i,!0);break;case"textarea":Q(r),ie(r);break;case"select":case"option":break;default:"function"===typeof i.onClick&&(r.onclick=Xr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=oe(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[fa]=t,e[ma]=r,Po(e,t,!1,!1),t.stateNode=e;e:{switch(s=be(n,r),n){case"dialog":Ar("cancel",e),Ar("close",e),a=r;break;case"iframe":case"object":case"embed":Ar("load",e),a=r;break;case"video":case"audio":for(a=0;a<Lr.length;a++)Ar(Lr[a],e);a=r;break;case"source":Ar("error",e),a=r;break;case"img":case"image":case"link":Ar("error",e),Ar("load",e),a=r;break;case"details":Ar("toggle",e),a=r;break;case"input":Y(e,r),a=J(e,r),Ar("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=M({},r,{value:void 0}),Ar("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Ar("invalid",e)}for(i in ye(n,a),c=a)if(c.hasOwnProperty(i)){var u=c[i];"style"===i?ge(e,u):"dangerouslySetInnerHTML"===i?null!=(u=u?u.__html:void 0)&&de(e,u):"children"===i?"string"===typeof u?("textarea"!==n||""!==u)&&fe(e,u):"number"===typeof u&&fe(e,""+u):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(o.hasOwnProperty(i)?null!=u&&"onScroll"===i&&Ar("scroll",e):null!=u&&b(e,i,u,s))}switch(n){case"input":Q(e),X(e,r,!1);break;case"textarea":Q(e),ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+$(r.value));break;case"select":e.multiple=!!r.multiple,null!=(i=r.value)?ne(e,!!r.multiple,i,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Xr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Qo(t),null;case 6:if(e&&null!=t.stateNode)Io(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(l(166));if(n=Kl(ql.current),Kl(Hl.current),dl(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(i=r.nodeValue!==n)&&null!==(e=nl))switch(e.tag){case 3:Zr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Zr(r.nodeValue,n,0!==(1&e.mode))}i&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return Qo(t),null;case 13:if(ka(Xl),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(al&&null!==rl&&0!==(1&t.mode)&&0===(128&t.flags))fl(),ml(),t.flags|=98560,i=!1;else if(i=dl(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(l(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(l(317));i[fa]=t}else ml(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Qo(t),i=!1}else null!==ll&&(ic(ll),ll=null),i=!0;if(!i)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&Xl.current)?0===Is&&(Is=3):hc())),null!==t.updateQueue&&(t.flags|=4),Qo(t),null);case 4:return Yl(),Oo(e,t),null===e&&Wr(t.stateNode.containerInfo),Qo(t),null;case 10:return Cl(t.type._context),Qo(t),null;case 19:if(ka(Xl),null===(i=t.memoizedState))return Qo(t),null;if(r=0!==(128&t.flags),null===(s=i.rendering))if(r)Ho(i,!1);else{if(0!==Is||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=ei(e))){for(t.flags|=128,Ho(i,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(i=n).flags&=14680066,null===(s=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return za(Xl,1&Xl.current|2),t.child}e=e.sibling}null!==i.tail&&Ge()>Ws&&(t.flags|=128,r=!0,Ho(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ei(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Ho(i,!0),null===i.tail&&"hidden"===i.tailMode&&!s.alternate&&!al)return Qo(t),null}else 2*Ge()-i.renderingStartTime>Ws&&1073741824!==n&&(t.flags|=128,r=!0,Ho(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=i.last)?n.sibling=s:t.child=s,i.last=s)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Ge(),t.sibling=null,n=Xl.current,za(Xl,r?1&n|2:1&n),t):(Qo(t),null);case 22:case 23:return dc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Os)&&(Qo(t),6&t.subtreeFlags&&(t.flags|=8192)):Qo(t),null;case 24:case 25:return null}throw Error(l(156,t.tag))}function Ko(e,t){switch(tl(t),t.tag){case 1:return Oa(t.type)&&Ra(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Yl(),ka(_a),ka(Ea),ni(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Zl(t),null;case 13:if(ka(Xl),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(l(340));ml()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return ka(Xl),null;case 4:return Yl(),null;case 10:return Cl(t.type._context),null;case 22:case 23:return dc(),null;default:return null}}Po=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Oo=function(){},Ro=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Kl(Hl.current);var l,i=null;switch(n){case"input":a=J(e,a),r=J(e,r),i=[];break;case"select":a=M({},a,{value:void 0}),r=M({},r,{value:void 0}),i=[];break;case"textarea":a=re(e,a),r=re(e,r),i=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Xr)}for(u in ye(n,r),n=null,a)if(!r.hasOwnProperty(u)&&a.hasOwnProperty(u)&&null!=a[u])if("style"===u){var s=a[u];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(o.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var c=r[u];if(s=null!=a?a[u]:void 0,r.hasOwnProperty(u)&&c!==s&&(null!=c||null!=s))if("style"===u)if(s){for(l in s)!s.hasOwnProperty(l)||c&&c.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in c)c.hasOwnProperty(l)&&s[l]!==c[l]&&(n||(n={}),n[l]=c[l])}else n||(i||(i=[]),i.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,s=s?s.__html:void 0,null!=c&&s!==c&&(i=i||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(i=i||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(o.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Ar("scroll",e),i||s===c||(i=[])):(i=i||[]).push(u,c))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}},Io=function(e,t,n,r){n!==r&&(t.flags|=4)};var Jo=!1,Yo=!1,Go="function"===typeof WeakSet?WeakSet:Set,Zo=null;function Xo(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){wc(e,t,r)}else n.current=null}function es(e,t,n){try{n()}catch(r){wc(e,t,r)}}var ts=!1;function ns(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var l=a.destroy;a.destroy=void 0,void 0!==l&&es(t,n,l)}a=a.next}while(a!==r)}}function rs(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function as(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function ls(e){var t=e.alternate;null!==t&&(e.alternate=null,ls(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[ma],delete t[ha],delete t[ga],delete t[va])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function is(e){return 5===e.tag||3===e.tag||4===e.tag}function os(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||is(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ss(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Xr));else if(4!==r&&null!==(e=e.child))for(ss(e,t,n),e=e.sibling;null!==e;)ss(e,t,n),e=e.sibling}function cs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cs(e,t,n),e=e.sibling;null!==e;)cs(e,t,n),e=e.sibling}var us=null,ds=!1;function fs(e,t,n){for(n=n.child;null!==n;)ms(e,t,n),n=n.sibling}function ms(e,t,n){if(lt&&"function"===typeof lt.onCommitFiberUnmount)try{lt.onCommitFiberUnmount(at,n)}catch(o){}switch(n.tag){case 5:Yo||Xo(n,t);case 6:var r=us,a=ds;us=null,fs(e,t,n),ds=a,null!==(us=r)&&(ds?(e=us,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):us.removeChild(n.stateNode));break;case 18:null!==us&&(ds?(e=us,n=n.stateNode,8===e.nodeType?sa(e.parentNode,n):1===e.nodeType&&sa(e,n),Wt(e)):sa(us,n.stateNode));break;case 4:r=us,a=ds,us=n.stateNode.containerInfo,ds=!0,fs(e,t,n),us=r,ds=a;break;case 0:case 11:case 14:case 15:if(!Yo&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var l=a,i=l.destroy;l=l.tag,void 0!==i&&(0!==(2&l)||0!==(4&l))&&es(n,t,i),a=a.next}while(a!==r)}fs(e,t,n);break;case 1:if(!Yo&&(Xo(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(o){wc(n,t,o)}fs(e,t,n);break;case 21:fs(e,t,n);break;case 22:1&n.mode?(Yo=(r=Yo)||null!==n.memoizedState,fs(e,t,n),Yo=r):fs(e,t,n);break;default:fs(e,t,n)}}function ps(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Go),t.forEach((function(t){var r=Ec.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function hs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var i=e,o=t,s=o;e:for(;null!==s;){switch(s.tag){case 5:us=s.stateNode,ds=!1;break e;case 3:case 4:us=s.stateNode.containerInfo,ds=!0;break e}s=s.return}if(null===us)throw Error(l(160));ms(i,o,a),us=null,ds=!1;var c=a.alternate;null!==c&&(c.return=null),a.return=null}catch(u){wc(a,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gs(t,e),t=t.sibling}function gs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(hs(t,e),vs(e),4&r){try{ns(3,e,e.return),rs(3,e)}catch(g){wc(e,e.return,g)}try{ns(5,e,e.return)}catch(g){wc(e,e.return,g)}}break;case 1:hs(t,e),vs(e),512&r&&null!==n&&Xo(n,n.return);break;case 5:if(hs(t,e),vs(e),512&r&&null!==n&&Xo(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(g){wc(e,e.return,g)}}if(4&r&&null!=(a=e.stateNode)){var i=e.memoizedProps,o=null!==n?n.memoizedProps:i,s=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===s&&"radio"===i.type&&null!=i.name&&G(a,i),be(s,o);var u=be(s,i);for(o=0;o<c.length;o+=2){var d=c[o],f=c[o+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):b(a,d,f,u)}switch(s){case"input":Z(a,i);break;case"textarea":le(a,i);break;case"select":var m=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!i.multiple;var p=i.value;null!=p?ne(a,!!i.multiple,p,!1):m!==!!i.multiple&&(null!=i.defaultValue?ne(a,!!i.multiple,i.defaultValue,!0):ne(a,!!i.multiple,i.multiple?[]:"",!1))}a[ma]=i}catch(g){wc(e,e.return,g)}}break;case 6:if(hs(t,e),vs(e),4&r){if(null===e.stateNode)throw Error(l(162));a=e.stateNode,i=e.memoizedProps;try{a.nodeValue=i}catch(g){wc(e,e.return,g)}}break;case 3:if(hs(t,e),vs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Wt(t.containerInfo)}catch(g){wc(e,e.return,g)}break;case 4:default:hs(t,e),vs(e);break;case 13:hs(t,e),vs(e),8192&(a=e.child).flags&&(i=null!==a.memoizedState,a.stateNode.isHidden=i,!i||null!==a.alternate&&null!==a.alternate.memoizedState||(Bs=Ge())),4&r&&ps(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Yo=(u=Yo)||d,hs(t,e),Yo=u):hs(t,e),vs(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&0!==(1&e.mode))for(Zo=e,d=e.child;null!==d;){for(f=Zo=d;null!==Zo;){switch(p=(m=Zo).child,m.tag){case 0:case 11:case 14:case 15:ns(4,m,m.return);break;case 1:Xo(m,m.return);var h=m.stateNode;if("function"===typeof h.componentWillUnmount){r=m,n=m.return;try{t=r,h.props=t.memoizedProps,h.state=t.memoizedState,h.componentWillUnmount()}catch(g){wc(r,n,g)}}break;case 5:Xo(m,m.return);break;case 22:if(null!==m.memoizedState){js(f);continue}}null!==p?(p.return=m,Zo=p):js(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,u?"function"===typeof(i=a.style).setProperty?i.setProperty("display","none","important"):i.display="none":(s=f.stateNode,o=void 0!==(c=f.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,s.style.display=he("display",o))}catch(g){wc(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(g){wc(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:hs(t,e),vs(e),4&r&&ps(e);case 21:}}function vs(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(is(n)){var r=n;break e}n=n.return}throw Error(l(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),cs(e,os(e),a);break;case 3:case 4:var i=r.stateNode.containerInfo;ss(e,os(e),i);break;default:throw Error(l(161))}}catch(o){wc(e,e.return,o)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function ys(e,t,n){Zo=e,bs(e,t,n)}function bs(e,t,n){for(var r=0!==(1&e.mode);null!==Zo;){var a=Zo,l=a.child;if(22===a.tag&&r){var i=null!==a.memoizedState||Jo;if(!i){var o=a.alternate,s=null!==o&&null!==o.memoizedState||Yo;o=Jo;var c=Yo;if(Jo=i,(Yo=s)&&!c)for(Zo=a;null!==Zo;)s=(i=Zo).child,22===i.tag&&null!==i.memoizedState?Ss(a):null!==s?(s.return=i,Zo=s):Ss(a);for(;null!==l;)Zo=l,bs(l,t,n),l=l.sibling;Zo=a,Jo=o,Yo=c}xs(e)}else 0!==(8772&a.subtreeFlags)&&null!==l?(l.return=a,Zo=l):xs(e)}}function xs(e){for(;null!==Zo;){var t=Zo;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Yo||rs(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Yo)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:to(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&Vl(t,i,r);break;case 3:var o=t.updateQueue;if(null!==o){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Vl(t,o,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Wt(f)}}}break;default:throw Error(l(163))}Yo||512&t.flags&&as(t)}catch(m){wc(t,t.return,m)}}if(t===e){Zo=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zo=n;break}Zo=t.return}}function js(e){for(;null!==Zo;){var t=Zo;if(t===e){Zo=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zo=n;break}Zo=t.return}}function Ss(e){for(;null!==Zo;){var t=Zo;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rs(4,t)}catch(s){wc(t,n,s)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(s){wc(t,a,s)}}var l=t.return;try{as(t)}catch(s){wc(t,l,s)}break;case 5:var i=t.return;try{as(t)}catch(s){wc(t,i,s)}}}catch(s){wc(t,t.return,s)}if(t===e){Zo=null;break}var o=t.sibling;if(null!==o){o.return=t.return,Zo=o;break}Zo=t.return}}var Ns,ws=Math.ceil,ks=x.ReactCurrentDispatcher,zs=x.ReactCurrentOwner,Cs=x.ReactCurrentBatchConfig,Es=0,_s=null,Ts=null,Ps=0,Os=0,Rs=wa(0),Is=0,Ls=null,Ds=0,Ms=0,Fs=0,As=null,Us=null,Bs=0,Ws=1/0,Vs=null,$s=!1,Hs=null,Qs=null,qs=!1,Ks=null,Js=0,Ys=0,Gs=null,Zs=-1,Xs=0;function ec(){return 0!==(6&Es)?Ge():-1!==Zs?Zs:Zs=Ge()}function tc(e){return 0===(1&e.mode)?1:0!==(2&Es)&&0!==Ps?Ps&-Ps:null!==hl.transition?(0===Xs&&(Xs=ht()),Xs):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Yt(e.type)}function nc(e,t,n,r){if(50<Ys)throw Ys=0,Gs=null,Error(l(185));vt(e,n,r),0!==(2&Es)&&e===_s||(e===_s&&(0===(2&Es)&&(Ms|=n),4===Is&&oc(e,Ps)),rc(e,r),1===n&&0===Es&&0===(1&t.mode)&&(Ws=Ge()+500,Aa&&Wa()))}function rc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=e.pendingLanes;0<l;){var i=31-it(l),o=1<<i,s=a[i];-1===s?0!==(o&n)&&0===(o&r)||(a[i]=mt(o,t)):s<=t&&(e.expiredLanes|=o),l&=~o}}(e,t);var r=ft(e,e===_s?Ps:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){Aa=!0,Ba(e)}(sc.bind(null,e)):Ba(sc.bind(null,e)),ia((function(){0===(6&Es)&&Wa()})),n=null;else{switch(xt(r)){case 1:n=Xe;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=_c(n,ac.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ac(e,t){if(Zs=-1,Xs=0,0!==(6&Es))throw Error(l(327));var n=e.callbackNode;if(Sc()&&e.callbackNode!==n)return null;var r=ft(e,e===_s?Ps:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gc(e,r);else{t=r;var a=Es;Es|=2;var i=pc();for(_s===e&&Ps===t||(Vs=null,Ws=Ge()+500,fc(e,t));;)try{yc();break}catch(s){mc(e,s)}zl(),ks.current=i,Es=a,null!==Ts?t=0:(_s=null,Ps=0,t=Is)}if(0!==t){if(2===t&&(0!==(a=pt(e))&&(r=a,t=lc(e,a))),1===t)throw n=Ls,fc(e,0),oc(e,r),rc(e,Ge()),n;if(6===t)oc(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!or(l(),a))return!1}catch(o){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=gc(e,r))&&(0!==(i=pt(e))&&(r=i,t=lc(e,i))),1===t))throw n=Ls,fc(e,0),oc(e,r),rc(e,Ge()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(l(345));case 2:case 5:jc(e,Us,Vs);break;case 3:if(oc(e,r),(130023424&r)===r&&10<(t=Bs+500-Ge())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){ec(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(jc.bind(null,e,Us,Vs),t);break}jc(e,Us,Vs);break;case 4:if(oc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var o=31-it(r);i=1<<o,(o=t[o])>a&&(a=o),r&=~i}if(r=a,10<(r=(120>(r=Ge()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*ws(r/1960))-r)){e.timeoutHandle=ra(jc.bind(null,e,Us,Vs),r);break}jc(e,Us,Vs);break;default:throw Error(l(329))}}}return rc(e,Ge()),e.callbackNode===n?ac.bind(null,e):null}function lc(e,t){var n=As;return e.current.memoizedState.isDehydrated&&(fc(e,t).flags|=256),2!==(e=gc(e,t))&&(t=Us,Us=n,null!==t&&ic(t)),e}function ic(e){null===Us?Us=e:Us.push.apply(Us,e)}function oc(e,t){for(t&=~Fs,t&=~Ms,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function sc(e){if(0!==(6&Es))throw Error(l(327));Sc();var t=ft(e,0);if(0===(1&t))return rc(e,Ge()),null;var n=gc(e,t);if(0!==e.tag&&2===n){var r=pt(e);0!==r&&(t=r,n=lc(e,r))}if(1===n)throw n=Ls,fc(e,0),oc(e,t),rc(e,Ge()),n;if(6===n)throw Error(l(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,jc(e,Us,Vs),rc(e,Ge()),null}function cc(e,t){var n=Es;Es|=1;try{return e(t)}finally{0===(Es=n)&&(Ws=Ge()+500,Aa&&Wa())}}function uc(e){null!==Ks&&0===Ks.tag&&0===(6&Es)&&Sc();var t=Es;Es|=1;var n=Cs.transition,r=bt;try{if(Cs.transition=null,bt=1,e)return e()}finally{bt=r,Cs.transition=n,0===(6&(Es=t))&&Wa()}}function dc(){Os=Rs.current,ka(Rs)}function fc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Ts)for(n=Ts.return;null!==n;){var r=n;switch(tl(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Ra();break;case 3:Yl(),ka(_a),ka(Ea),ni();break;case 5:Zl(r);break;case 4:Yl();break;case 13:case 19:ka(Xl);break;case 10:Cl(r.type._context);break;case 22:case 23:dc()}n=n.return}if(_s=e,Ts=e=Rc(e.current,null),Ps=Os=t,Is=0,Ls=null,Fs=Ms=Ds=0,Us=As=null,null!==Pl){for(t=0;t<Pl.length;t++)if(null!==(r=(n=Pl[t]).interleaved)){n.interleaved=null;var a=r.next,l=n.pending;if(null!==l){var i=l.next;l.next=a,r.next=i}n.pending=r}Pl=null}return e}function mc(e,t){for(;;){var n=Ts;try{if(zl(),ri.current=Gi,ci){for(var r=ii.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}ci=!1}if(li=0,si=oi=ii=null,ui=!1,di=0,zs.current=null,null===n||null===n.return){Is=1,Ls=t,Ts=null;break}e:{var i=e,o=n.return,s=n,c=t;if(t=Ps,s.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var m=d.alternate;m?(d.updateQueue=m.updateQueue,d.memoizedState=m.memoizedState,d.lanes=m.lanes):(d.updateQueue=null,d.memoizedState=null)}var p=go(o);if(null!==p){p.flags&=-257,vo(p,o,s,0,t),1&p.mode&&ho(i,u,t),c=u;var h=(t=p).updateQueue;if(null===h){var g=new Set;g.add(c),t.updateQueue=g}else h.add(c);break e}if(0===(1&t)){ho(i,u,t),hc();break e}c=Error(l(426))}else if(al&&1&s.mode){var v=go(o);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),vo(v,o,s,0,t),pl(so(c,s));break e}}i=c=so(c,s),4!==Is&&(Is=2),null===As?As=[i]:As.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,Bl(i,mo(0,c,t));break e;case 1:s=c;var y=i.type,b=i.stateNode;if(0===(128&i.flags)&&("function"===typeof y.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===Qs||!Qs.has(b)))){i.flags|=65536,t&=-t,i.lanes|=t,Bl(i,po(i,s,t));break e}}i=i.return}while(null!==i)}xc(n)}catch(x){t=x,Ts===n&&null!==n&&(Ts=n=n.return);continue}break}}function pc(){var e=ks.current;return ks.current=Gi,null===e?Gi:e}function hc(){0!==Is&&3!==Is&&2!==Is||(Is=4),null===_s||0===(268435455&Ds)&&0===(268435455&Ms)||oc(_s,Ps)}function gc(e,t){var n=Es;Es|=2;var r=pc();for(_s===e&&Ps===t||(Vs=null,fc(e,t));;)try{vc();break}catch(a){mc(e,a)}if(zl(),Es=n,ks.current=r,null!==Ts)throw Error(l(261));return _s=null,Ps=0,Is}function vc(){for(;null!==Ts;)bc(Ts)}function yc(){for(;null!==Ts&&!Je();)bc(Ts)}function bc(e){var t=Ns(e.alternate,e,Os);e.memoizedProps=e.pendingProps,null===t?xc(e):Ts=t,zs.current=null}function xc(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=qo(n,t,Os)))return void(Ts=n)}else{if(null!==(n=Ko(n,t)))return n.flags&=32767,void(Ts=n);if(null===e)return Is=6,void(Ts=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Ts=t);Ts=t=e}while(null!==t);0===Is&&(Is=5)}function jc(e,t,n){var r=bt,a=Cs.transition;try{Cs.transition=null,bt=1,function(e,t,n,r){do{Sc()}while(null!==Ks);if(0!==(6&Es))throw Error(l(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-it(n),l=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~l}}(e,i),e===_s&&(Ts=_s=null,Ps=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||qs||(qs=!0,_c(tt,(function(){return Sc(),null}))),i=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||i){i=Cs.transition,Cs.transition=null;var o=bt;bt=1;var s=Es;Es|=4,zs.current=null,function(e,t){if(ea=$t,mr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(j){n=null;break e}var o=0,s=-1,c=-1,u=0,d=0,f=e,m=null;t:for(;;){for(var p;f!==n||0!==a&&3!==f.nodeType||(s=o+a),f!==i||0!==r&&3!==f.nodeType||(c=o+r),3===f.nodeType&&(o+=f.nodeValue.length),null!==(p=f.firstChild);)m=f,f=p;for(;;){if(f===e)break t;if(m===n&&++u===a&&(s=o),m===i&&++d===r&&(c=o),null!==(p=f.nextSibling))break;m=(f=m).parentNode}f=p}n=-1===s||-1===c?null:{start:s,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},$t=!1,Zo=t;null!==Zo;)if(e=(t=Zo).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Zo=e;else for(;null!==Zo;){t=Zo;try{var h=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==h){var g=h.memoizedProps,v=h.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:to(t.type,g),v);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(l(163))}}catch(j){wc(t,t.return,j)}if(null!==(e=t.sibling)){e.return=t.return,Zo=e;break}Zo=t.return}h=ts,ts=!1}(e,n),gs(n,e),pr(ta),$t=!!ea,ta=ea=null,e.current=n,ys(n,e,a),Ye(),Es=s,bt=o,Cs.transition=i}else e.current=n;if(qs&&(qs=!1,Ks=e,Js=a),i=e.pendingLanes,0===i&&(Qs=null),function(e){if(lt&&"function"===typeof lt.onCommitFiberRoot)try{lt.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),rc(e,Ge()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if($s)throw $s=!1,e=Hs,Hs=null,e;0!==(1&Js)&&0!==e.tag&&Sc(),i=e.pendingLanes,0!==(1&i)?e===Gs?Ys++:(Ys=0,Gs=e):Ys=0,Wa()}(e,t,n,r)}finally{Cs.transition=a,bt=r}return null}function Sc(){if(null!==Ks){var e=xt(Js),t=Cs.transition,n=bt;try{if(Cs.transition=null,bt=16>e?16:e,null===Ks)var r=!1;else{if(e=Ks,Ks=null,Js=0,0!==(6&Es))throw Error(l(331));var a=Es;for(Es|=4,Zo=e.current;null!==Zo;){var i=Zo,o=i.child;if(0!==(16&Zo.flags)){var s=i.deletions;if(null!==s){for(var c=0;c<s.length;c++){var u=s[c];for(Zo=u;null!==Zo;){var d=Zo;switch(d.tag){case 0:case 11:case 15:ns(8,d,i)}var f=d.child;if(null!==f)f.return=d,Zo=f;else for(;null!==Zo;){var m=(d=Zo).sibling,p=d.return;if(ls(d),d===u){Zo=null;break}if(null!==m){m.return=p,Zo=m;break}Zo=p}}}var h=i.alternate;if(null!==h){var g=h.child;if(null!==g){h.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Zo=i}}if(0!==(2064&i.subtreeFlags)&&null!==o)o.return=i,Zo=o;else e:for(;null!==Zo;){if(0!==(2048&(i=Zo).flags))switch(i.tag){case 0:case 11:case 15:ns(9,i,i.return)}var y=i.sibling;if(null!==y){y.return=i.return,Zo=y;break e}Zo=i.return}}var b=e.current;for(Zo=b;null!==Zo;){var x=(o=Zo).child;if(0!==(2064&o.subtreeFlags)&&null!==x)x.return=o,Zo=x;else e:for(o=b;null!==Zo;){if(0!==(2048&(s=Zo).flags))try{switch(s.tag){case 0:case 11:case 15:rs(9,s)}}catch(S){wc(s,s.return,S)}if(s===o){Zo=null;break e}var j=s.sibling;if(null!==j){j.return=s.return,Zo=j;break e}Zo=s.return}}if(Es=a,Wa(),lt&&"function"===typeof lt.onPostCommitFiberRoot)try{lt.onPostCommitFiberRoot(at,e)}catch(S){}r=!0}return r}finally{bt=n,Cs.transition=t}}return!1}function Nc(e,t,n){e=Al(e,t=mo(0,t=so(n,t),1),1),t=ec(),null!==e&&(vt(e,1,t),rc(e,t))}function wc(e,t,n){if(3===e.tag)Nc(e,e,n);else for(;null!==t;){if(3===t.tag){Nc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Qs||!Qs.has(r))){t=Al(t,e=po(t,e=so(n,e),1),1),e=ec(),null!==t&&(vt(t,1,e),rc(t,e));break}}t=t.return}}function kc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ec(),e.pingedLanes|=e.suspendedLanes&n,_s===e&&(Ps&n)===n&&(4===Is||3===Is&&(130023424&Ps)===Ps&&500>Ge()-Bs?fc(e,0):Fs|=n),rc(e,t)}function zc(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var n=ec();null!==(e=Il(e,t))&&(vt(e,t,n),rc(e,n))}function Cc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),zc(e,n)}function Ec(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(l(314))}null!==r&&r.delete(t),zc(e,n)}function _c(e,t){return qe(e,t)}function Tc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Pc(e,t,n,r){return new Tc(e,t,n,r)}function Oc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Rc(e,t){var n=e.alternate;return null===n?((n=Pc(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ic(e,t,n,r,a,i){var o=2;if(r=e,"function"===typeof e)Oc(e)&&(o=1);else if("string"===typeof e)o=5;else e:switch(e){case N:return Lc(n.children,a,i,t);case w:o=8,a|=8;break;case k:return(e=Pc(12,n,t,2|a)).elementType=k,e.lanes=i,e;case _:return(e=Pc(13,n,t,a)).elementType=_,e.lanes=i,e;case T:return(e=Pc(19,n,t,a)).elementType=T,e.lanes=i,e;case R:return Dc(n,a,i,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case z:o=10;break e;case C:o=9;break e;case E:o=11;break e;case P:o=14;break e;case O:o=16,r=null;break e}throw Error(l(130,null==e?e:typeof e,""))}return(t=Pc(o,n,t,a)).elementType=e,t.type=r,t.lanes=i,t}function Lc(e,t,n,r){return(e=Pc(7,e,r,t)).lanes=n,e}function Dc(e,t,n,r){return(e=Pc(22,e,r,t)).elementType=R,e.lanes=n,e.stateNode={isHidden:!1},e}function Mc(e,t,n){return(e=Pc(6,e,null,t)).lanes=n,e}function Fc(e,t,n){return(t=Pc(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ac(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Uc(e,t,n,r,a,l,i,o,s){return e=new Ac(e,t,n,o,s),1===t?(t=1,!0===l&&(t|=8)):t=0,l=Pc(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Dl(l),e}function Bc(e){if(!e)return Ca;e:{if(We(e=e._reactInternals)!==e||1!==e.tag)throw Error(l(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Oa(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(l(171))}if(1===e.tag){var n=e.type;if(Oa(n))return La(e,n,t)}return t}function Wc(e,t,n,r,a,l,i,o,s){return(e=Uc(n,r,!0,e,0,l,0,o,s)).context=Bc(null),n=e.current,(l=Fl(r=ec(),a=tc(n))).callback=void 0!==t&&null!==t?t:null,Al(n,l,a),e.current.lanes=a,vt(e,a,r),rc(e,r),e}function Vc(e,t,n,r){var a=t.current,l=ec(),i=tc(a);return n=Bc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Fl(l,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Al(a,t,i))&&(nc(e,a,i,l),Ul(e,a,i)),i}function $c(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Hc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Qc(e,t){Hc(e,t),(e=e.alternate)&&Hc(e,t)}Ns=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||_a.current)bo=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bo=!1,function(e,t,n){switch(t.tag){case 3:_o(t),ml();break;case 5:Gl(t);break;case 1:Oa(t.type)&&Da(t);break;case 4:Jl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;za(Sl,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(za(Xl,1&Xl.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Mo(e,t,n):(za(Xl,1&Xl.current),null!==(e=$o(e,t,n))?e.sibling:null);za(Xl,1&Xl.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Wo(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),za(Xl,Xl.current),r)break;return null;case 22:case 23:return t.lanes=0,wo(e,t,n)}return $o(e,t,n)}(e,t,n);bo=0!==(131072&e.flags)}else bo=!1,al&&0!==(1048576&t.flags)&&Xa(t,Qa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Vo(e,t),e=t.pendingProps;var a=Pa(t,Ea.current);_l(t,n),a=hi(null,t,r,e,a,n);var i=gi();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Oa(r)?(i=!0,Da(t)):i=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Dl(t),a.updater=ro,t.stateNode=a,a._reactInternals=t,oo(t,r,e,n),t=Eo(null,t,r,!0,i,n)):(t.tag=0,al&&i&&el(t),xo(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Vo(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Oc(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===E)return 11;if(e===P)return 14}return 2}(r),e=to(r,e),a){case 0:t=zo(null,t,r,e,n);break e;case 1:t=Co(null,t,r,e,n);break e;case 11:t=jo(null,t,r,e,n);break e;case 14:t=So(null,t,r,to(r.type,e),n);break e}throw Error(l(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,zo(e,t,r,a=t.elementType===r?a:to(r,a),n);case 1:return r=t.type,a=t.pendingProps,Co(e,t,r,a=t.elementType===r?a:to(r,a),n);case 3:e:{if(_o(t),null===e)throw Error(l(387));r=t.pendingProps,a=(i=t.memoizedState).element,Ml(e,t),Wl(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=To(e,t,r,n,a=so(Error(l(423)),t));break e}if(r!==a){t=To(e,t,r,n,a=so(Error(l(424)),t));break e}for(rl=ca(t.stateNode.containerInfo.firstChild),nl=t,al=!0,ll=null,n=jl(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ml(),r===a){t=$o(e,t,n);break e}xo(e,t,r,n)}t=t.child}return t;case 5:return Gl(t),null===e&&cl(t),r=t.type,a=t.pendingProps,i=null!==e?e.memoizedProps:null,o=a.children,na(r,a)?o=null:null!==i&&na(r,i)&&(t.flags|=32),ko(e,t),xo(e,t,o,n),t.child;case 6:return null===e&&cl(t),null;case 13:return Mo(e,t,n);case 4:return Jl(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=xl(t,null,r,n):xo(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,jo(e,t,r,a=t.elementType===r?a:to(r,a),n);case 7:return xo(e,t,t.pendingProps,n),t.child;case 8:case 12:return xo(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,i=t.memoizedProps,o=a.value,za(Sl,r._currentValue),r._currentValue=o,null!==i)if(or(i.value,o)){if(i.children===a.children&&!_a.current){t=$o(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var s=i.dependencies;if(null!==s){o=i.child;for(var c=s.firstContext;null!==c;){if(c.context===r){if(1===i.tag){(c=Fl(-1,n&-n)).tag=2;var u=i.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}i.lanes|=n,null!==(c=i.alternate)&&(c.lanes|=n),El(i.return,n,t),s.lanes|=n;break}c=c.next}}else if(10===i.tag)o=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(o=i.return))throw Error(l(341));o.lanes|=n,null!==(s=o.alternate)&&(s.lanes|=n),El(o,n,t),o=i.sibling}else o=i.child;if(null!==o)o.return=i;else for(o=i;null!==o;){if(o===t){o=null;break}if(null!==(i=o.sibling)){i.return=o.return,o=i;break}o=o.return}i=o}xo(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,_l(t,n),r=r(a=Tl(a)),t.flags|=1,xo(e,t,r,n),t.child;case 14:return a=to(r=t.type,t.pendingProps),So(e,t,r,a=to(r.type,a),n);case 15:return No(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:to(r,a),Vo(e,t),t.tag=1,Oa(r)?(e=!0,Da(t)):e=!1,_l(t,n),lo(t,r,a),oo(t,r,a,n),Eo(null,t,r,!0,e,n);case 19:return Wo(e,t,n);case 22:return wo(e,t,n)}throw Error(l(156,t.tag))};var qc="function"===typeof reportError?reportError:function(e){console.error(e)};function Kc(e){this._internalRoot=e}function Jc(e){this._internalRoot=e}function Yc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Gc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zc(){}function Xc(e,t,n,r,a){var l=n._reactRootContainer;if(l){var i=l;if("function"===typeof a){var o=a;a=function(){var e=$c(i);o.call(e)}}Vc(t,i,e,a)}else i=function(e,t,n,r,a){if(a){if("function"===typeof r){var l=r;r=function(){var e=$c(i);l.call(e)}}var i=Wc(t,r,e,0,null,!1,0,"",Zc);return e._reactRootContainer=i,e[pa]=i.current,Wr(8===e.nodeType?e.parentNode:e),uc(),i}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var o=r;r=function(){var e=$c(s);o.call(e)}}var s=Uc(e,0,!1,null,0,!1,0,"",Zc);return e._reactRootContainer=s,e[pa]=s.current,Wr(8===e.nodeType?e.parentNode:e),uc((function(){Vc(t,s,n,r)})),s}(n,t,e,a,r);return $c(i)}Jc.prototype.render=Kc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(l(409));Vc(e,t,null,null)},Jc.prototype.unmount=Kc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uc((function(){Vc(null,e,null,null)})),t[pa]=null}},Jc.prototype.unstable_scheduleHydration=function(e){if(e){var t=wt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Rt.length&&0!==t&&t<Rt[n].priority;n++);Rt.splice(n,0,e),0===n&&Mt(e)}},jt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),rc(t,Ge()),0===(6&Es)&&(Ws=Ge()+500,Wa()))}break;case 13:uc((function(){var t=Il(e,1);if(null!==t){var n=ec();nc(t,e,1,n)}})),Qc(e,1)}},St=function(e){if(13===e.tag){var t=Il(e,134217728);if(null!==t)nc(t,e,134217728,ec());Qc(e,134217728)}},Nt=function(e){if(13===e.tag){var t=tc(e),n=Il(e,t);if(null!==n)nc(n,e,t,ec());Qc(e,t)}},wt=function(){return bt},kt=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Se=function(e,t,n){switch(t){case"input":if(Z(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=ja(r);if(!a)throw Error(l(90));q(r),Z(r,a)}}}break;case"textarea":le(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ee=cc,_e=uc;var eu={usingClientEntryPoint:!1,Events:[ba,xa,ja,ze,Ce,cc]},tu={findFiberByHostInstance:ya,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nu={bundleType:tu.bundleType,version:tu.version,rendererPackageName:tu.rendererPackageName,rendererConfig:tu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=He(e))?null:e.stateNode},findFiberByHostInstance:tu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ru=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ru.isDisabled&&ru.supportsFiber)try{at=ru.inject(nu),lt=ru}catch(ue){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=eu,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Yc(t))throw Error(l(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Yc(e))throw Error(l(299));var n=!1,r="",a=qc;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Uc(e,1,!1,null,0,n,0,r,a),e[pa]=t.current,Wr(8===e.nodeType?e.parentNode:e),new Kc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return e=null===(e=He(t))?null:e.stateNode},t.flushSync=function(e){return uc(e)},t.hydrate=function(e,t,n){if(!Gc(t))throw Error(l(200));return Xc(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Yc(e))throw Error(l(405));var r=null!=n&&n.hydratedSources||null,a=!1,i="",o=qc;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(o=n.onRecoverableError)),t=Wc(t,null,e,1,null!=n?n:null,a,0,i,o),e[pa]=t.current,Wr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Jc(t)},t.render=function(e,t,n){if(!Gc(t))throw Error(l(200));return Xc(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Gc(e))throw Error(l(40));return!!e._reactRootContainer&&(uc((function(){Xc(null,null,e,!1,(function(){e._reactRootContainer=null,e[pa]=null}))})),!0)},t.unstable_batchedUpdates=cc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Gc(n))throw Error(l(200));if(null==e||void 0===e._reactInternals)throw Error(l(38));return Xc(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var l=Object.create(null);n.r(l);var i={};e=e||[null,t({}),t([]),t(t)];for(var o=2&a&&r;"object"==typeof o&&!~e.indexOf(o);o=t(o))Object.getOwnPropertyNames(o).forEach((e=>i[e]=()=>r[e]));return i.default=()=>r,n.d(l,i),l}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r=n(43),a=n.t(r,2),l=n(391);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}function s(e,t,n){return(t=o(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var d,f=n(950),m=n.t(f,2);function p(){return p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},p.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(d||(d={}));const h="popstate";function g(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function v(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function y(e,t){return{usr:e.state,key:e.key,idx:t}}function b(e,t,n,r){return void 0===n&&(n=null),p({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?j(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function x(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function j(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function S(e,t,n,r){void 0===r&&(r={});let{window:a=document.defaultView,v5Compat:l=!1}=r,i=a.history,o=d.Pop,s=null,c=u();function u(){return(i.state||{idx:null}).idx}function f(){o=d.Pop;let e=u(),t=null==e?null:e-c;c=e,s&&s({action:o,location:v.location,delta:t})}function m(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,n="string"===typeof e?e:x(e);return n=n.replace(/ $/,"%20"),g(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==c&&(c=0,i.replaceState(p({},i.state,{idx:c}),""));let v={get action(){return o},get location(){return e(a,i)},listen(e){if(s)throw new Error("A history only accepts one active listener");return a.addEventListener(h,f),s=e,()=>{a.removeEventListener(h,f),s=null}},createHref:e=>t(a,e),createURL:m,encodeLocation(e){let t=m(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){o=d.Push;let r=b(v.location,e,t);n&&n(r,e),c=u()+1;let f=y(r,c),m=v.createHref(r);try{i.pushState(f,"",m)}catch(p){if(p instanceof DOMException&&"DataCloneError"===p.name)throw p;a.location.assign(m)}l&&s&&s({action:o,location:v.location,delta:1})},replace:function(e,t){o=d.Replace;let r=b(v.location,e,t);n&&n(r,e),c=u();let a=y(r,c),f=v.createHref(r);i.replaceState(a,"",f),l&&s&&s({action:o,location:v.location,delta:0})},go:e=>i.go(e)};return v}var N;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(N||(N={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function w(e,t,n){return void 0===n&&(n="/"),k(e,t,n,!1)}function k(e,t,n,r){let a=A(("string"===typeof t?j(t):t).pathname||"/",n);if(null==a)return null;let l=z(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(l);let i=null;for(let o=0;null==i&&o<l.length;++o){let e=F(a);i=D(l[o],e,r)}return i}function z(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,l)=>{let i={relativePath:void 0===l?e.path||"":l,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(g(i.relativePath.startsWith(r),'Absolute route path "'+i.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(r.length));let o=$([r,i.relativePath]),s=n.concat(i);e.children&&e.children.length>0&&(g(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+o+'".'),z(e.children,t,s,o)),(null!=e.path||e.index)&&t.push({path:o,score:L(o,e.index),routesMeta:s})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of C(e.path))a(e,t,r);else a(e,t)})),t}function C(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(0===r.length)return a?[l,""]:[l];let i=C(r.join("/")),o=[];return o.push(...i.map((e=>""===e?l:[l,e].join("/")))),a&&o.push(...i),o.map((t=>e.startsWith("/")&&""===t?"/":t))}const E=/^:[\w-]+$/,_=3,T=2,P=1,O=10,R=-2,I=e=>"*"===e;function L(e,t){let n=e.split("/"),r=n.length;return n.some(I)&&(r+=R),t&&(r+=T),n.filter((e=>!I(e))).reduce(((e,t)=>e+(E.test(t)?_:""===t?P:O)),r)}function D(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},l="/",i=[];for(let o=0;o<r.length;++o){let e=r[o],s=o===r.length-1,c="/"===l?t:t.slice(l.length)||"/",u=M({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},c),d=e.route;if(!u&&s&&n&&!r[r.length-1].route.index&&(u=M({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(a,u.params),i.push({params:a,pathname:$([l,u.pathname]),pathnameBase:H($([l,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(l=$([l,u.pathnameBase]))}return i}function M(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);v("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let l=new RegExp(a,t?void 0:"i");return[l,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),o=a.slice(1);return{params:r.reduce(((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=o[n]||"";i=l.slice(0,l.length-e.length).replace(/(.)\/+$/,"$1")}const s=o[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e}),{}),pathname:l,pathnameBase:i,pattern:e}}function F(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return v(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function A(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function U(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function B(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function W(e,t){let n=B(e);return t?n.map(((e,t)=>t===n.length-1?e.pathname:e.pathnameBase)):n.map((e=>e.pathnameBase))}function V(e,t,n,r){let a;void 0===r&&(r=!1),"string"===typeof e?a=j(e):(a=p({},e),g(!a.pathname||!a.pathname.includes("?"),U("?","pathname","search",a)),g(!a.pathname||!a.pathname.includes("#"),U("#","pathname","hash",a)),g(!a.search||!a.search.includes("#"),U("#","search","hash",a)));let l,i=""===e||""===a.pathname,o=i?"/":a.pathname;if(null==o)l=n;else{let e=t.length-1;if(!r&&o.startsWith("..")){let t=o.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}l=e>=0?t[e]:"/"}let s=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?j(e):e,l=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:l,search:Q(r),hash:q(a)}}(a,l),c=o&&"/"!==o&&o.endsWith("/"),u=(i||"."===o)&&n.endsWith("/");return s.pathname.endsWith("/")||!c&&!u||(s.pathname+="/"),s}const $=e=>e.join("/").replace(/\/\/+/g,"/"),H=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Q=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",q=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function K(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const J=["post","put","patch","delete"],Y=(new Set(J),["get",...J]);new Set(Y),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function G(){return G=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},G.apply(this,arguments)}const Z=r.createContext(null);const X=r.createContext(null);const ee=r.createContext(null);const te=r.createContext(null);const ne=r.createContext({outlet:null,matches:[],isDataRoute:!1});const re=r.createContext(null);function ae(){return null!=r.useContext(te)}function le(){return ae()||g(!1),r.useContext(te).location}function ie(e){r.useContext(ee).static||r.useLayoutEffect(e)}function oe(){let{isDataRoute:e}=r.useContext(ne);return e?function(){let{router:e}=ve(he.UseNavigateStable),t=be(ge.UseNavigateStable),n=r.useRef(!1);return ie((()=>{n.current=!0})),r.useCallback((function(r,a){void 0===a&&(a={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,G({fromRouteId:t},a)))}),[e,t])}():function(){ae()||g(!1);let e=r.useContext(Z),{basename:t,future:n,navigator:a}=r.useContext(ee),{matches:l}=r.useContext(ne),{pathname:i}=le(),o=JSON.stringify(W(l,n.v7_relativeSplatPath)),s=r.useRef(!1);return ie((()=>{s.current=!0})),r.useCallback((function(n,r){if(void 0===r&&(r={}),!s.current)return;if("number"===typeof n)return void a.go(n);let l=V(n,JSON.parse(o),i,"path"===r.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:$([t,l.pathname])),(r.replace?a.replace:a.push)(l,r.state,r)}),[t,a,o,i,e])}()}function se(e,t){let{relative:n}=void 0===t?{}:t,{future:a}=r.useContext(ee),{matches:l}=r.useContext(ne),{pathname:i}=le(),o=JSON.stringify(W(l,a.v7_relativeSplatPath));return r.useMemo((()=>V(e,JSON.parse(o),i,"path"===n)),[e,o,i,n])}function ce(e,t,n,a){ae()||g(!1);let{navigator:l}=r.useContext(ee),{matches:i}=r.useContext(ne),o=i[i.length-1],s=o?o.params:{},c=(o&&o.pathname,o?o.pathnameBase:"/");o&&o.route;let u,f=le();if(t){var m;let e="string"===typeof t?j(t):t;"/"===c||(null==(m=e.pathname)?void 0:m.startsWith(c))||g(!1),u=e}else u=f;let p=u.pathname||"/",h=p;if("/"!==c){let e=c.replace(/^\//,"").split("/");h="/"+p.replace(/^\//,"").split("/").slice(e.length).join("/")}let v=w(e,{pathname:h});let y=pe(v&&v.map((e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:$([c,l.encodeLocation?l.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:$([c,l.encodeLocation?l.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),i,n,a);return t&&y?r.createElement(te.Provider,{value:{location:G({pathname:"/",search:"",hash:"",state:null,key:"default"},u),navigationType:d.Pop}},y):y}function ue(){let e=function(){var e;let t=r.useContext(re),n=ye(ge.UseRouteError),a=be(ge.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[a]}(),t=K(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",l={padding:"0.5rem",backgroundColor:a};return r.createElement(r.Fragment,null,r.createElement("h2",null,"Unexpected Application Error!"),r.createElement("h3",{style:{fontStyle:"italic"}},t),n?r.createElement("pre",{style:l},n):null,null)}const de=r.createElement(ue,null);class fe extends r.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?r.createElement(ne.Provider,{value:this.props.routeContext},r.createElement(re.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function me(e){let{routeContext:t,match:n,children:a}=e,l=r.useContext(Z);return l&&l.static&&l.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(l.staticContext._deepestRenderedBoundaryId=n.route.id),r.createElement(ne.Provider,{value:t},a)}function pe(e,t,n,a){var l;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===a&&(a=null),null==e){var i;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(i=a)&&i.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let o=e,s=null==(l=n)?void 0:l.errors;if(null!=s){let e=o.findIndex((e=>e.route.id&&void 0!==(null==s?void 0:s[e.route.id])));e>=0||g(!1),o=o.slice(0,Math.min(o.length,e+1))}let c=!1,u=-1;if(n&&a&&a.v7_partialHydration)for(let r=0;r<o.length;r++){let e=o[r];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(u=r),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){c=!0,o=u>=0?o.slice(0,u+1):[o[0]];break}}}return o.reduceRight(((e,a,l)=>{let i,d=!1,f=null,m=null;var p;n&&(i=s&&a.route.id?s[a.route.id]:void 0,f=a.route.errorElement||de,c&&(u<0&&0===l?(p="route-fallback",!1||xe[p]||(xe[p]=!0),d=!0,m=null):u===l&&(d=!0,m=a.route.hydrateFallbackElement||null)));let h=t.concat(o.slice(0,l+1)),g=()=>{let t;return t=i?f:d?m:a.route.Component?r.createElement(a.route.Component,null):a.route.element?a.route.element:e,r.createElement(me,{match:a,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(a.route.ErrorBoundary||a.route.errorElement||0===l)?r.createElement(fe,{location:n.location,revalidation:n.revalidation,component:f,error:i,children:g(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):g()}),null)}var he=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(he||{}),ge=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ge||{});function ve(e){let t=r.useContext(Z);return t||g(!1),t}function ye(e){let t=r.useContext(X);return t||g(!1),t}function be(e){let t=function(){let e=r.useContext(ne);return e||g(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||g(!1),n.route.id}const xe={};function je(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}a.startTransition;function Se(e){g(!1)}function Ne(e){let{basename:t="/",children:n=null,location:a,navigationType:l=d.Pop,navigator:i,static:o=!1,future:s}=e;ae()&&g(!1);let c=t.replace(/^\/*/,"/"),u=r.useMemo((()=>({basename:c,navigator:i,static:o,future:G({v7_relativeSplatPath:!1},s)})),[c,s,i,o]);"string"===typeof a&&(a=j(a));let{pathname:f="/",search:m="",hash:p="",state:h=null,key:v="default"}=a,y=r.useMemo((()=>{let e=A(f,c);return null==e?null:{location:{pathname:e,search:m,hash:p,state:h,key:v},navigationType:l}}),[c,f,m,p,h,v,l]);return null==y?null:r.createElement(ee.Provider,{value:u},r.createElement(te.Provider,{children:n,value:y}))}function we(e){let{children:t,location:n}=e;return ce(ke(t),n)}new Promise((()=>{}));r.Component;function ke(e,t){void 0===t&&(t=[]);let n=[];return r.Children.forEach(e,((e,a)=>{if(!r.isValidElement(e))return;let l=[...t,a];if(e.type===r.Fragment)return void n.push.apply(n,ke(e.props.children,l));e.type!==Se&&g(!1),e.props.index&&e.props.children&&g(!1);let i={id:e.props.id||l.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=ke(e.props.children,l)),n.push(i)})),n}function ze(){return ze=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ze.apply(this,arguments)}function Ce(e,t){if(null==e)return{};var n,r,a={},l=Object.keys(e);for(r=0;r<l.length;r++)n=l[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const Ee=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(Ye){}new Map;const _e=a.startTransition;m.flushSync,a.useId;function Te(e){let{basename:t,children:n,future:a,window:l}=e,i=r.useRef();var o;null==i.current&&(i.current=(void 0===(o={window:l,v5Compat:!0})&&(o={}),S((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return b("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:x(t)}),null,o)));let s=i.current,[c,u]=r.useState({action:s.action,location:s.location}),{v7_startTransition:d}=a||{},f=r.useCallback((e=>{d&&_e?_e((()=>u(e))):u(e)}),[u,d]);return r.useLayoutEffect((()=>s.listen(f)),[s,f]),r.useEffect((()=>je(a)),[a]),r.createElement(Ne,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:s,future:a})}const Pe="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,Oe=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Re=r.forwardRef((function(e,t){let n,{onClick:a,relative:l,reloadDocument:i,replace:o,state:s,target:c,to:u,preventScrollReset:d,viewTransition:f}=e,m=Ce(e,Ee),{basename:p}=r.useContext(ee),h=!1;if("string"===typeof u&&Oe.test(u)&&(n=u,Pe))try{let e=new URL(window.location.href),t=u.startsWith("//")?new URL(e.protocol+u):new URL(u),n=A(t.pathname,p);t.origin===e.origin&&null!=n?u=n+t.search+t.hash:h=!0}catch(Ye){}let v=function(e,t){let{relative:n}=void 0===t?{}:t;ae()||g(!1);let{basename:a,navigator:l}=r.useContext(ee),{hash:i,pathname:o,search:s}=se(e,{relative:n}),c=o;return"/"!==a&&(c="/"===o?a:$([a,o])),l.createHref({pathname:c,search:s,hash:i})}(u,{relative:l}),y=function(e,t){let{target:n,replace:a,state:l,preventScrollReset:i,relative:o,viewTransition:s}=void 0===t?{}:t,c=oe(),u=le(),d=se(e,{relative:o});return r.useCallback((t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==a?a:x(u)===x(d);c(e,{replace:n,state:l,preventScrollReset:i,relative:o,viewTransition:s})}}),[u,c,d,a,l,n,e,i,o,s])}(u,{replace:o,state:s,target:c,preventScrollReset:d,relative:l,viewTransition:f});return r.createElement("a",ze({},m,{href:n||v,onClick:h||i?a:function(e){a&&a(e),e.defaultPrevented||y(e)},ref:t,target:c}))}));var Ie,Le;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Ie||(Ie={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(Le||(Le={}));const De={API_BASE_URL:{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"http://localhost:3001",API_ENDPOINTS:{ORDERS:"/api/orders",CUSTOMERS:"/api/customers",PRODUCTS:"/api/products",STRIPE_CREATE_CUSTOMER:"/api/stripe/create-customer",STRIPE_CREATE_PRODUCT:"/api/stripe/create-product",STRIPE_CREATE_PRICE:"/api/stripe/create-price",STRIPE_CREATE_PAYMENT_LINK:"/api/stripe/create-payment-link",STRIPE_WEBHOOK:"/api/stripe/webhook"},APP:{NAME:"Pizzeria Jasmin",VERSION:"1.0.0",DESCRIPTION:"Sistema di ordinazione online per Pizzeria Jasmin - Missaglia"},PIZZERIA:{NAME:"Pizzeria Jasmin",ADDRESS:"Via Marconi, 5, Missaglia (LC)",PHONE:"039 9240339",HOURS:"17:30-22:00",SPECIALTIES:["Pizza","Kebab"]},DELIVERY_ZONES:{Missaglia:0,Casatenovo:2.5,"Barzan\xf2":3,Cremella:3.5,"Monticello Brianza":4,"Vigan\xf2":4.5,Altro:5},ORDER_CONFIG:{MIN_ORDER_FOR_FREE_DELIVERY:15,DEFAULT_PREPARATION_TIME:10,PIZZA_PREPARATION_TIME:3,KEBAB_PREPARATION_TIME:2,DELIVERY_TIME:20},CUSTOMIZATIONS:{SIZE:{grande:2},DOUGH:{integrale:1,"senza-glutine":3}}},Me=e=>{const t=De.API_ENDPOINTS[e];if(!t)throw new Error("Endpoint '".concat(e,"' non trovato nella configurazione"));return(e=>"".concat(De.API_BASE_URL).concat(e))(t)},Fe=De;var Ae=n(579);const Ue=function(e){let{cartCount:t,onDashboardClick:n}=e;const r=le();return(0,Ae.jsxs)("header",{className:"header",children:[(0,Ae.jsxs)("div",{className:"header-content",children:[(0,Ae.jsxs)("div",{className:"logo",children:[(0,Ae.jsx)("i",{className:"fas fa-pizza-slice"}),(0,Ae.jsx)("span",{children:"Pizzeria Jasmin"})]}),(0,Ae.jsxs)("nav",{className:"nav",children:[(0,Ae.jsxs)(Re,{to:"/",className:"/"===r.pathname?"active":"",children:[(0,Ae.jsx)("i",{className:"fas fa-home"})," Home"]}),(0,Ae.jsxs)(Re,{to:"/menu",className:"/menu"===r.pathname?"active":"",children:[(0,Ae.jsx)("i",{className:"fas fa-utensils"})," Menu"]}),(0,Ae.jsxs)(Re,{to:"/cart",className:"cart-icon",children:[(0,Ae.jsx)("i",{className:"fas fa-shopping-cart"}),t>0&&(0,Ae.jsx)("span",{className:"cart-count",children:t})]}),(0,Ae.jsxs)("button",{onClick:n,className:"btn btn-secondary",style:{marginLeft:"1rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-tachometer-alt"})," Dashboard"]})]})]}),(0,Ae.jsx)("div",{style:{background:"rgba(0,0,0,0.1)",padding:"0.5rem 0",textAlign:"center",fontSize:"0.9rem"},children:(0,Ae.jsxs)("div",{className:"header-content",children:[(0,Ae.jsxs)("span",{children:[(0,Ae.jsx)("i",{className:"fas fa-map-marker-alt"})," Via Marconi, 5 - Missaglia (LC)"]}),(0,Ae.jsxs)("span",{style:{margin:"0 2rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-phone"})," 039 9240339"]}),(0,Ae.jsxs)("span",{children:[(0,Ae.jsx)("i",{className:"fas fa-clock"})," Aperto: 17:30 - 22:30"]})]})})]})};const Be=function(){return(0,Ae.jsxs)("div",{className:"fade-in",children:[(0,Ae.jsx)("section",{style:{background:"linear-gradient(rgba(211, 47, 47, 0.8), rgba(183, 28, 28, 0.8)), url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 600'%3E%3Cpath fill='%23ff9800' d='M0,300 Q300,100 600,300 T1200,300 L1200,600 L0,600 Z'/%3E%3C/svg%3E\")",backgroundSize:"cover",backgroundPosition:"center",color:"white",padding:"4rem 0",textAlign:"center"},children:(0,Ae.jsxs)("div",{className:"container",children:[(0,Ae.jsx)("h1",{style:{fontSize:"3rem",marginBottom:"1rem",fontWeight:"bold"},children:"Benvenuti alla Pizzeria Jasmin"}),(0,Ae.jsx)("p",{style:{fontSize:"1.2rem",marginBottom:"2rem",maxWidth:"600px",margin:"0 auto 2rem"},children:"La migliore pizza e kebab di Missaglia, ora anche a domicilio! Ingredienti freschi, sapori autentici e consegna veloce."}),(0,Ae.jsxs)("div",{className:"flex flex-center gap-2",children:[(0,Ae.jsxs)(Re,{to:"/menu",className:"btn btn-secondary",style:{fontSize:"1.1rem",padding:"1rem 2rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-utensils"})," Ordina Ora"]}),(0,Ae.jsxs)("a",{href:"tel:0399240339",className:"btn btn-outline",style:{fontSize:"1.1rem",padding:"1rem 2rem",color:"white",borderColor:"white"},children:[(0,Ae.jsx)("i",{className:"fas fa-phone"})," Chiama: 039 9240339"]})]})]})}),(0,Ae.jsxs)("section",{className:"container",children:[(0,Ae.jsx)("h2",{className:"text-center mb-3",style:{fontSize:"2.5rem",color:"var(--primary-color)"},children:"I Nostri Servizi"}),(0,Ae.jsxs)("div",{className:"grid grid-3",children:[(0,Ae.jsx)("div",{className:"card text-center",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsx)("i",{className:"fas fa-motorcycle",style:{fontSize:"3rem",color:"var(--primary-color)",marginBottom:"1rem"}}),(0,Ae.jsx)("h3",{children:"Consegna a Domicilio"}),(0,Ae.jsx)("p",{children:"Consegniamo in tutta Missaglia e comuni limitrofi. Consegna veloce in 20-30 minuti!"}),(0,Ae.jsx)("div",{className:"mt-2",children:(0,Ae.jsx)("span",{className:"status-badge",style:{background:"var(--success-color)",color:"white"},children:"Gratis sopra \u20ac15"})})]})}),(0,Ae.jsx)("div",{className:"card text-center",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsx)("i",{className:"fas fa-shopping-bag",style:{fontSize:"3rem",color:"var(--secondary-color)",marginBottom:"1rem"}}),(0,Ae.jsx)("h3",{children:"Ritiro in Sede"}),(0,Ae.jsx)("p",{children:"Ordina online e ritira direttamente in pizzeria. Risparmia sui costi di consegna!"}),(0,Ae.jsx)("div",{className:"mt-2",children:(0,Ae.jsx)("span",{className:"status-badge",style:{background:"var(--secondary-color)",color:"white"},children:"Sconto 10%"})})]})}),(0,Ae.jsx)("div",{className:"card text-center",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsx)("i",{className:"fas fa-clock",style:{fontSize:"3rem",color:"var(--warning-color)",marginBottom:"1rem"}}),(0,Ae.jsx)("h3",{children:"Orari Flessibili"}),(0,Ae.jsx)("p",{children:"Aperto tutti i giorni dalle 17:30 alle 22:30. Ordina quando vuoi!"}),(0,Ae.jsx)("div",{className:"mt-2",children:(0,Ae.jsx)("span",{className:"status-badge",style:{background:"var(--warning-color)",color:"white"},children:"7 giorni su 7"})})]})})]})]}),(0,Ae.jsx)("section",{style:{background:"var(--background-light)",padding:"3rem 0",marginTop:"3rem"},children:(0,Ae.jsxs)("div",{className:"container",children:[(0,Ae.jsx)("h2",{className:"text-center mb-3",style:{fontSize:"2.5rem",color:"var(--primary-color)"},children:"Le Nostre Specialit\xe0"}),(0,Ae.jsxs)("div",{className:"grid grid-2",children:[(0,Ae.jsx)("div",{className:"card",children:(0,Ae.jsx)("div",{className:"card-body",children:(0,Ae.jsxs)("div",{className:"flex gap-2",children:[(0,Ae.jsx)("i",{className:"fas fa-pizza-slice",style:{fontSize:"2rem",color:"var(--primary-color)"}}),(0,Ae.jsxs)("div",{children:[(0,Ae.jsx)("h3",{children:"Pizza Artigianale"}),(0,Ae.jsx)("p",{children:"Impasto fatto in casa, lievitazione naturale e ingredienti di prima qualit\xe0. Dalle classiche Margherita e Marinara alle nostre specialit\xe0 creative."}),(0,Ae.jsxs)("ul",{style:{marginTop:"1rem",paddingLeft:"1rem"},children:[(0,Ae.jsx)("li",{children:"Impasto lievitato 24h"}),(0,Ae.jsx)("li",{children:"Mozzarella di bufala"}),(0,Ae.jsx)("li",{children:"Pomodoro San Marzano"}),(0,Ae.jsx)("li",{children:"Cottura in forno a legna"})]})]})]})})}),(0,Ae.jsx)("div",{className:"card",children:(0,Ae.jsx)("div",{className:"card-body",children:(0,Ae.jsxs)("div",{className:"flex gap-2",children:[(0,Ae.jsx)("i",{className:"fas fa-drumstick-bite",style:{fontSize:"2rem",color:"var(--secondary-color)"}}),(0,Ae.jsxs)("div",{children:[(0,Ae.jsx)("h3",{children:"Kebab Autentico"}),(0,Ae.jsx)("p",{children:"Carne fresca marinata con spezie orientali, cotta alla griglia e servita con verdure fresche e salse fatte in casa."}),(0,Ae.jsxs)("ul",{style:{marginTop:"1rem",paddingLeft:"1rem"},children:[(0,Ae.jsx)("li",{children:"Carne 100% italiana"}),(0,Ae.jsx)("li",{children:"Marinatura 12h"}),(0,Ae.jsx)("li",{children:"Verdure fresche giornaliere"}),(0,Ae.jsx)("li",{children:"Salse artigianali"})]})]})]})})})]})]})}),(0,Ae.jsxs)("section",{className:"container text-center",style:{padding:"3rem 0"},children:[(0,Ae.jsx)("h2",{style:{fontSize:"2rem",marginBottom:"1rem",color:"var(--primary-color)"},children:"Pronto per ordinare?"}),(0,Ae.jsx)("p",{style:{fontSize:"1.1rem",marginBottom:"2rem",color:"var(--text-light)"},children:"Scopri il nostro menu completo e ordina la tua pizza o kebab preferito!"}),(0,Ae.jsxs)(Re,{to:"/menu",className:"btn btn-primary",style:{fontSize:"1.2rem",padding:"1rem 3rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-utensils"})," Vai al Menu"]})]}),(0,Ae.jsx)("section",{style:{background:"var(--primary-color)",color:"white",padding:"2rem 0"},children:(0,Ae.jsx)("div",{className:"container",children:(0,Ae.jsxs)("div",{className:"grid grid-3 text-center",children:[(0,Ae.jsxs)("div",{children:[(0,Ae.jsx)("i",{className:"fas fa-star",style:{fontSize:"2rem",marginBottom:"1rem"}}),(0,Ae.jsx)("h4",{children:"Qualit\xe0 Garantita"}),(0,Ae.jsx)("p",{children:"Ingredienti freschi e di prima qualit\xe0 per ogni ordine"})]}),(0,Ae.jsxs)("div",{children:[(0,Ae.jsx)("i",{className:"fas fa-shipping-fast",style:{fontSize:"2rem",marginBottom:"1rem"}}),(0,Ae.jsx)("h4",{children:"Consegna Veloce"}),(0,Ae.jsx)("p",{children:"Tempi di consegna ottimizzati, massimo 30 minuti"})]}),(0,Ae.jsxs)("div",{children:[(0,Ae.jsx)("i",{className:"fas fa-heart",style:{fontSize:"2rem",marginBottom:"1rem"}}),(0,Ae.jsx)("h4",{children:"Soddisfazione Cliente"}),(0,Ae.jsx)("p",{children:"La tua soddisfazione \xe8 la nostra priorit\xe0 assoluta"})]})]})})})]})},We={pizze:[{id:"pizza-1",name:"Margherita",description:"Pomodoro, mozzarella, basilico fresco",price:6.5,category:"pizza",preparationTime:3,image:"/images/pizza-margherita.svg"},{id:"pizza-2",name:"Marinara",description:"Pomodoro, aglio, origano, olio EVO",price:5.5,category:"pizza",preparationTime:3,image:"/images/pizza-marinara.svg"},{id:"pizza-3",name:"Diavola",description:"Pomodoro, mozzarella, salame piccante",price:7.5,category:"pizza",preparationTime:3,image:"/images/pizza-diavola.svg"},{id:"pizza-4",name:"Capricciosa",description:"Pomodoro, mozzarella, prosciutto, funghi, carciofi, olive",price:8.5,category:"pizza",preparationTime:4,image:"/images/pizza-capricciosa.svg"},{id:"pizza-5",name:"Quattro Stagioni",description:"Pomodoro, mozzarella, prosciutto, funghi, carciofi, olive",price:8.5,category:"pizza",preparationTime:4,image:"/images/pizza-quattro-stagioni.svg"},{id:"pizza-6",name:"Quattro Formaggi",description:"Mozzarella, gorgonzola, parmigiano, fontina",price:8,category:"pizza",preparationTime:3,image:"/images/pizza-quattro-formaggi.svg"},{id:"pizza-7",name:"Prosciutto e Funghi",description:"Pomodoro, mozzarella, prosciutto cotto, funghi",price:7.5,category:"pizza",preparationTime:3,image:"/images/pizza-prosciutto-funghi.svg"},{id:"pizza-8",name:"Vegetariana",description:"Pomodoro, mozzarella, zucchine, melanzane, peperoni",price:7.5,category:"pizza",preparationTime:4,image:"/images/pizza-vegetariana.svg"}],kebab:[{id:"kebab-1",name:"Kebab nel Pane",description:"Carne di pollo e vitello, verdure fresche, salse",price:5.5,category:"kebab",preparationTime:2,image:"/images/kebab-pane.svg"},{id:"kebab-2",name:"Kebab nel Piatto",description:"Carne kebab, insalata, pomodori, cipolle, patatine",price:7,category:"kebab",preparationTime:3,image:"\ud83c\udf7d\ufe0f"},{id:"kebab-3",name:"Piadina Kebab",description:"Piadina farcita con kebab, verdure e salse",price:6,category:"kebab",preparationTime:2,image:"\ud83c\udf2f"},{id:"kebab-4",name:"Kebab Box",description:"Kebab, riso basmati, verdure, salse orientali",price:8,category:"kebab",preparationTime:3,image:"\ud83d\udce6"}],bevande:[{id:"drink-1",name:"Coca Cola",description:"Lattina 33cl",price:2,category:"bevanda",preparationTime:0,image:"\ud83e\udd64"},{id:"drink-2",name:"Birra Moretti",description:"Bottiglia 66cl",price:3.5,category:"bevanda",preparationTime:0,image:"\ud83c\udf7a"},{id:"drink-3",name:"Acqua Naturale",description:"Bottiglia 50cl",price:1.5,category:"bevanda",preparationTime:0,image:"\ud83d\udca7"},{id:"drink-4",name:"Aranciata",description:"Lattina 33cl",price:2,category:"bevanda",preparationTime:0,image:"\ud83c\udf4a"}],dolci:[{id:"dessert-1",name:"Tiramis\xf9",description:"Tiramis\xf9 della casa",price:4.5,category:"dolce",preparationTime:1,image:"\ud83c\udf70"},{id:"dessert-2",name:"Nutella Pizza",description:"Pizza dolce con Nutella",price:5,category:"dolce",preparationTime:3,image:"\ud83c\udf6b"}]};const Ve=function(e){var t;let{addToCart:n}=e;const[a,l]=(0,r.useState)("pizze"),[i,o]=(0,r.useState)(null),[s,c]=(0,r.useState)({}),d=(e,t,n)=>{c((r=>u(u({},r),{},{[e]:u(u({},r[e]),{},{[t]:n})})))};return(0,Ae.jsxs)("div",{className:"container fade-in",children:[(0,Ae.jsxs)("h1",{className:"text-center mb-3",style:{color:"var(--primary-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-utensils"})," Menu Pizzeria Jasmin"]}),(0,Ae.jsx)("p",{className:"text-center mb-3",style:{fontSize:"1.1rem",color:"var(--text-light)"},children:"Scegli tra le nostre specialit\xe0 preparate con ingredienti freschi e di qualit\xe0"}),(0,Ae.jsx)("div",{className:"flex flex-center gap-2 mb-3",style:{flexWrap:"wrap"},children:[{key:"pizze",name:"Pizze",icon:"\ud83c\udf55"},{key:"kebab",name:"Kebab",icon:"\ud83e\udd59"},{key:"bevande",name:"Bevande",icon:"\ud83e\udd64"},{key:"dolci",name:"Dolci",icon:"\ud83c\udf70"}].map((e=>(0,Ae.jsxs)("button",{onClick:()=>l(e.key),className:"btn ".concat(a===e.key?"btn-primary":"btn-outline"),style:{minWidth:"120px"},children:[(0,Ae.jsx)("span",{style:{fontSize:"1.2rem",marginRight:"0.5rem"},children:e.icon}),e.name]},e.key)))}),(0,Ae.jsx)("div",{className:"grid grid-3",children:null===(t=We[a])||void 0===t?void 0:t.map((e=>{var t,r,a,l;return(0,Ae.jsx)("div",{className:"card",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsx)("div",{className:"text-center mb-2",children:e.image.startsWith("/images/")?(0,Ae.jsx)("img",{src:e.image,alt:e.name,style:{width:"80px",height:"80px",objectFit:"contain",borderRadius:"8px"}}):(0,Ae.jsx)("span",{style:{fontSize:"3rem"},children:e.image})}),(0,Ae.jsx)("h3",{className:"text-center mb-1",children:e.name}),(0,Ae.jsx)("p",{className:"text-center mb-2",style:{color:"var(--text-light)",fontSize:"0.9rem"},children:e.description}),(0,Ae.jsxs)("div",{className:"flex flex-between mb-2",children:[(0,Ae.jsxs)("span",{className:"status-badge",style:{background:"var(--secondary-color)",color:"white"},children:["\u20ac",e.price.toFixed(2)]}),(0,Ae.jsxs)("span",{className:"status-badge",style:{background:"var(--background-light)",color:"var(--text-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-clock"})," ",e.preparationTime,"min"]})]}),"pizza"===e.category&&(0,Ae.jsxs)("div",{className:"mb-2",children:[(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{className:"form-label",style:{fontSize:"0.9rem"},children:"Dimensione:"}),(0,Ae.jsxs)("select",{className:"form-select",value:(null===(t=s[e.id])||void 0===t?void 0:t.size)||"normale",onChange:t=>d(e.id,"size",t.target.value),style:{fontSize:"0.9rem",padding:"0.5rem"},children:[(0,Ae.jsx)("option",{value:"normale",children:"Normale"}),(0,Ae.jsx)("option",{value:"grande",children:"Grande (+\u20ac2.00)"})]})]}),(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{className:"form-label",style:{fontSize:"0.9rem"},children:"Impasto:"}),(0,Ae.jsxs)("select",{className:"form-select",value:(null===(r=s[e.id])||void 0===r?void 0:r.dough)||"normale",onChange:t=>d(e.id,"dough",t.target.value),style:{fontSize:"0.9rem",padding:"0.5rem"},children:[(0,Ae.jsx)("option",{value:"normale",children:"Normale"}),(0,Ae.jsx)("option",{value:"integrale",children:"Integrale (+\u20ac1.00)"}),(0,Ae.jsx)("option",{value:"senza-glutine",children:"Senza Glutine (+\u20ac3.00)"})]})]})]}),"kebab"===e.category&&(0,Ae.jsxs)("div",{className:"mb-2",children:[(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{className:"form-label",style:{fontSize:"0.9rem"},children:"Salsa:"}),(0,Ae.jsxs)("select",{className:"form-select",value:(null===(a=s[e.id])||void 0===a?void 0:a.sauce)||"yogurt",onChange:t=>d(e.id,"sauce",t.target.value),style:{fontSize:"0.9rem",padding:"0.5rem"},children:[(0,Ae.jsx)("option",{value:"yogurt",children:"Yogurt"}),(0,Ae.jsx)("option",{value:"piccante",children:"Piccante"}),(0,Ae.jsx)("option",{value:"ketchup",children:"Ketchup"}),(0,Ae.jsx)("option",{value:"maionese",children:"Maionese"})]})]}),(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{className:"form-label",style:{fontSize:"0.9rem"},children:"Piccantezza:"}),(0,Ae.jsxs)("select",{className:"form-select",value:(null===(l=s[e.id])||void 0===l?void 0:l.spice)||"normale",onChange:t=>d(e.id,"spice",t.target.value),style:{fontSize:"0.9rem",padding:"0.5rem"},children:[(0,Ae.jsx)("option",{value:"delicato",children:"Delicato"}),(0,Ae.jsx)("option",{value:"normale",children:"Normale"}),(0,Ae.jsx)("option",{value:"piccante",children:"Piccante"})]})]})]}),(0,Ae.jsxs)("button",{"data-item-id":e.id,onClick:()=>(e=>{const t=u(u({},e),{},{customizations:s[e.id]||{}});n(t),o(null),c((t=>{const n=u({},t);return delete n[e.id],n}));const r=document.querySelector('[data-item-id="'.concat(e.id,'"]'));r&&(r.style.transform="scale(0.95)",r.style.background="var(--success-color)",setTimeout((()=>{r.style.transform="scale(1)",r.style.background=""}),200))})(e),className:"btn btn-primary w-full",style:{transition:"all 0.2s"},children:[(0,Ae.jsx)("i",{className:"fas fa-plus"})," Aggiungi al Carrello"]})]})},e.id)}))}),(0,Ae.jsxs)("div",{className:"mt-3 p-3",style:{background:"var(--background-light)",borderRadius:"var(--border-radius)"},children:[(0,Ae.jsxs)("h4",{className:"text-center mb-2",style:{color:"var(--primary-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-info-circle"})," Informazioni Importanti"]}),(0,Ae.jsxs)("div",{className:"grid grid-2",children:[(0,Ae.jsxs)("div",{children:[(0,Ae.jsxs)("h5",{children:[(0,Ae.jsx)("i",{className:"fas fa-clock"})," Tempi di Preparazione"]}),(0,Ae.jsxs)("ul",{style:{fontSize:"0.9rem",color:"var(--text-light)"},children:[(0,Ae.jsx)("li",{children:"Pizza: 3-4 minuti"}),(0,Ae.jsx)("li",{children:"Kebab: 2-3 minuti"}),(0,Ae.jsx)("li",{children:"Dolci: 1-3 minuti"})]})]}),(0,Ae.jsxs)("div",{children:[(0,Ae.jsxs)("h5",{children:[(0,Ae.jsx)("i",{className:"fas fa-truck"})," Consegna"]}),(0,Ae.jsxs)("ul",{style:{fontSize:"0.9rem",color:"var(--text-light)"},children:[(0,Ae.jsx)("li",{children:"Missaglia: Gratuita sopra \u20ac15"}),(0,Ae.jsx)("li",{children:"Comuni limitrofi: \u20ac2-5"}),(0,Ae.jsx)("li",{children:"Tempo stimato: 20-30 min"})]})]})]})]})]})};const $e=function(e){let{cart:t,updateQuantity:n,removeFromCart:r,clearCart:a}=e;const l=e=>{let t=e.price;return e.customizations&&(e.customizations.size&&Fe.CUSTOMIZATIONS.SIZE[e.customizations.size]&&(t+=Fe.CUSTOMIZATIONS.SIZE[e.customizations.size]),e.customizations.dough&&Fe.CUSTOMIZATIONS.DOUGH[e.customizations.dough]&&(t+=Fe.CUSTOMIZATIONS.DOUGH[e.customizations.dough])),t},i=()=>t.reduce(((e,t)=>e+l(t)*t.quantity),0),o=()=>t.reduce(((e,t)=>e+t.quantity),0),s=e=>{if(!e)return"";const t=[];return e.size&&"normale"!==e.size&&t.push("".concat(e.size)),e.dough&&"normale"!==e.dough&&t.push("impasto ".concat(e.dough)),e.sauce&&"yogurt"!==e.sauce&&t.push("salsa ".concat(e.sauce)),e.spice&&"normale"!==e.spice&&t.push("".concat(e.spice)),t.length>0?" (".concat(t.join(", "),")"):""};return 0===t.length?(0,Ae.jsxs)("div",{className:"container fade-in",children:[(0,Ae.jsxs)("h1",{className:"text-center mb-3",style:{color:"var(--primary-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-shopping-cart"})," Carrello"]}),(0,Ae.jsxs)("div",{className:"text-center",style:{padding:"3rem 1rem"},children:[(0,Ae.jsx)("div",{style:{fontSize:"4rem",marginBottom:"1rem",opacity:.3},children:"\ud83d\uded2"}),(0,Ae.jsx)("h3",{style:{color:"var(--text-light)",marginBottom:"1rem"},children:"Il tuo carrello \xe8 vuoto"}),(0,Ae.jsx)("p",{style:{color:"var(--text-light)",marginBottom:"2rem"},children:"Aggiungi alcuni prodotti dal nostro menu per iniziare il tuo ordine"}),(0,Ae.jsxs)("a",{href:"/menu",className:"btn btn-primary",children:[(0,Ae.jsx)("i",{className:"fas fa-utensils"})," Vai al Menu"]})]})]}):(0,Ae.jsxs)("div",{className:"container fade-in",children:[(0,Ae.jsxs)("h1",{className:"text-center mb-3",style:{color:"var(--primary-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-shopping-cart"})," Carrello (",o()," ",1===o()?"articolo":"articoli",")"]}),(0,Ae.jsx)("div",{className:"mb-3",children:t.map(((e,t)=>(0,Ae.jsx)("div",{className:"card mb-2",children:(0,Ae.jsx)("div",{className:"card-body",children:(0,Ae.jsxs)("div",{className:"flex flex-between",children:[(0,Ae.jsx)("div",{className:"flex-1",children:(0,Ae.jsxs)("div",{className:"flex gap-2 mb-2",children:[(0,Ae.jsx)("span",{style:{fontSize:"1.5rem"},children:e.image}),(0,Ae.jsxs)("div",{children:[(0,Ae.jsx)("h4",{className:"mb-1",children:e.name}),(0,Ae.jsxs)("p",{style:{color:"var(--text-light)",fontSize:"0.9rem",margin:0},children:[e.description,s(e.customizations)]})]})]})}),(0,Ae.jsxs)("div",{className:"flex flex-column align-end gap-2",children:[(0,Ae.jsxs)("div",{className:"text-right",children:[(0,Ae.jsxs)("div",{style:{fontSize:"1.1rem",fontWeight:"bold",color:"var(--primary-color)"},children:["\u20ac",l(e).toFixed(2)]}),e.customizations&&Object.keys(e.customizations).length>0&&(0,Ae.jsxs)("div",{style:{fontSize:"0.8rem",color:"var(--text-light)"},children:["(base: \u20ac",e.price.toFixed(2),")"]})]}),(0,Ae.jsxs)("div",{className:"flex align-center gap-2",children:[(0,Ae.jsx)("button",{onClick:()=>n(e.cartId,e.quantity-1),className:"btn btn-outline",style:{width:"35px",height:"35px",padding:0,fontSize:"1.2rem"},disabled:e.quantity<=1,children:(0,Ae.jsx)("i",{className:"fas fa-minus"})}),(0,Ae.jsx)("span",{style:{minWidth:"40px",textAlign:"center",fontSize:"1.1rem",fontWeight:"bold"},children:e.quantity}),(0,Ae.jsx)("button",{onClick:()=>n(e.cartId,e.quantity+1),className:"btn btn-outline",style:{width:"35px",height:"35px",padding:0,fontSize:"1.2rem"},children:(0,Ae.jsx)("i",{className:"fas fa-plus"})})]}),(0,Ae.jsxs)("div",{style:{fontSize:"1.2rem",fontWeight:"bold",color:"var(--secondary-color)"},children:["\u20ac",(l(e)*e.quantity).toFixed(2)]}),(0,Ae.jsxs)("button",{onClick:()=>r(e.cartId),className:"btn btn-danger",style:{fontSize:"0.8rem",padding:"0.3rem 0.6rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-trash"})," Rimuovi"]})]})]})})},"".concat(e.id,"-").concat(t))))}),(0,Ae.jsx)("div",{className:"card",style:{background:"var(--background-light)"},children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("h3",{className:"text-center mb-3",style:{color:"var(--primary-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-receipt"})," Riepilogo Ordine"]}),(0,Ae.jsxs)("div",{className:"flex flex-between mb-2",children:[(0,Ae.jsxs)("span",{children:["Subtotale (",o()," ",1===o()?"articolo":"articoli","):"]}),(0,Ae.jsxs)("span",{style:{fontWeight:"bold"},children:["\u20ac",i().toFixed(2)]})]}),(0,Ae.jsxs)("div",{className:"flex flex-between mb-2",children:[(0,Ae.jsx)("span",{children:"Costi di consegna:"}),(0,Ae.jsx)("span",{style:{color:"var(--success-color)"},children:"Calcolati al checkout"})]}),(0,Ae.jsx)("hr",{style:{margin:"1rem 0",border:"1px solid var(--border-color)"}}),(0,Ae.jsxs)("div",{className:"flex flex-between mb-3",style:{fontSize:"1.2rem",fontWeight:"bold"},children:[(0,Ae.jsx)("span",{children:"Totale stimato:"}),(0,Ae.jsxs)("span",{style:{color:"var(--secondary-color)"},children:["\u20ac",i().toFixed(2)]})]}),(0,Ae.jsxs)("div",{className:"flex gap-2",children:[(0,Ae.jsxs)("button",{onClick:a,className:"btn btn-outline flex-1",children:[(0,Ae.jsx)("i",{className:"fas fa-trash"})," Svuota Carrello"]}),(0,Ae.jsxs)(Re,{to:"/checkout",className:"btn btn-primary flex-2",style:{textDecoration:"none",textAlign:"center"},children:[(0,Ae.jsx)("i",{className:"fas fa-credit-card"})," Procedi al Checkout"]})]}),(0,Ae.jsx)("div",{className:"text-center mt-2",children:(0,Ae.jsxs)(Re,{to:"/menu",style:{color:"var(--primary-color)",textDecoration:"none",fontSize:"0.9rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-arrow-left"})," Continua a fare acquisti"]})})]})}),(0,Ae.jsxs)("div",{className:"mt-3 p-3",style:{background:"var(--success-color)",color:"white",borderRadius:"var(--border-radius)",textAlign:"center"},children:[(0,Ae.jsxs)("h5",{children:[(0,Ae.jsx)("i",{className:"fas fa-info-circle"})," Informazioni Utili"]}),(0,Ae.jsxs)("p",{style:{margin:0,fontSize:"0.9rem"},children:["\ud83d\ude9a Consegna gratuita per ordini superiori a \u20ac",Fe.ORDER_CONFIG.MIN_ORDER_FOR_FREE_DELIVERY," a Missaglia",(0,Ae.jsx)("br",{}),"\u23f1\ufe0f Tempo di preparazione stimato: ",Math.max(Fe.ORDER_CONFIG.DEFAULT_PREPARATION_TIME,t.reduce(((e,t)=>e+t.preparationTime*t.quantity),0))," minuti"]})]})]})};const He=function(e){let{cart:t,addOrder:n,clearCart:a}=e;const l=oe(),[i,o]=(0,r.useState)("delivery"),[s,c]=(0,r.useState)({name:"",phone:"",email:"",address:"",city:"Missaglia",notes:""}),[d,f]=(0,r.useState)("asap"),[m,p]=(0,r.useState)(""),[h,g]=(0,r.useState)("cash"),[v,y]=(0,r.useState)({cardNumber:"",expiryDate:"",cvv:"",cardholderName:""}),[b,x]=(0,r.useState)(!0),[j,S]=(0,r.useState)(!1),[N,w]=(0,r.useState)(0),[k,z]=(0,r.useState)({}),[C,E]=(0,r.useState)(!1),[_,T]=(0,r.useState)({});(0,r.useEffect)((()=>{if("delivery"===i){const e=Fe.DELIVERY_ZONES[s.city]||Fe.DELIVERY_ZONES.Altro,n=t.reduce(((e,t)=>{let n=t.price;return t.customizations&&("grande"===t.customizations.size&&(n+=2),"integrale"===t.customizations.dough&&(n+=1),"senza-glutine"===t.customizations.dough&&(n+=3)),e+n*t.quantity}),0);"Missaglia"===s.city&&n>=Fe.ORDER_CONFIG.MIN_ORDER_FOR_FREE_DELIVERY?w(0):w(e)}else w(0)}),[i,s.city,t]);const P=e=>{let t=e.price;return e.customizations&&(e.customizations.size&&Fe.CUSTOMIZATIONS.SIZE[e.customizations.size]&&(t+=Fe.CUSTOMIZATIONS.SIZE[e.customizations.size]),e.customizations.dough&&Fe.CUSTOMIZATIONS.DOUGH[e.customizations.dough]&&(t+=Fe.CUSTOMIZATIONS.DOUGH[e.customizations.dough])),t},O=()=>t.reduce(((e,t)=>e+P(t)*t.quantity),0),R=()=>O()+N,I=()=>{const e=t.reduce(((e,t)=>e+t.preparationTime*t.quantity),0);return Math.max(Fe.ORDER_CONFIG.DEFAULT_PREPARATION_TIME,e)},L=(e,t)=>{switch(e){case"name":return t.trim()?"":"Nome richiesto";case"phone":const e=/^[+]?[0-9\s-()]{8,}$/;return t.trim()?e.test(t)?"":"Formato telefono non valido":"Telefono richiesto";case"email":return t&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)?"Email non valida":"";case"address":return"delivery"!==i||t.trim()?"":"Indirizzo richiesto per la consegna";default:return""}},D=(e,t)=>{if(c(u(u({},s),{},{[e]:t})),_[e]){const n=L(e,t);z((t=>u(u({},t),{},{[e]:n})))}},M=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(T((t=>u(u({},t),{},{[e]:!0}))),t){let t="";"cardNumber"===e?v.cardNumber.trim()?/^[0-9\s]{13,19}$/.test(v.cardNumber.replace(/\s/g,""))||(t="Numero carta non valido"):t="Numero carta richiesto":"expiryDate"===e?v.expiryDate.trim()?/^(0[1-9]|1[0-2])\/([0-9]{2})$/.test(v.expiryDate)||(t="Formato: MM/AA"):t="Data di scadenza richiesta":"cvv"===e?v.cvv.trim()?/^[0-9]{3,4}$/.test(v.cvv)||(t="CVV non valido"):t="CVV richiesto":"cardholderName"===e&&(v.cardholderName.trim()||(t="Nome titolare richiesto")),z((n=>u(u({},n),{},{[e]:t})))}else{const t=L(e,s[e]);z((n=>u(u({},n),{},{[e]:t})))}},F=async(e,n,r)=>{const a={customer:{name:s.name,phone:s.phone,email:s.email||null,address:"delivery"===i?s.address:null,city:"delivery"===i?s.city:null,notes:s.notes||null,stripe_customer_id:e},items:t.map((e=>({name:e.name,price:P(e),quantity:e.quantity,customizations:e.customizations||null}))),orderType:i,deliveryTime:"asap"===d?"Il Prima Possibile":m,paymentMethod:"stripe",deliveryFee:N,subtotal:O(),total:R(),stripe_product_id:n,stripe_price_id:r},l=await fetch("http://localhost:3000/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!l.ok)throw new Error("Errore salvataggio ordine");return await l.json()};return 0===t.length?(0,Ae.jsx)("div",{className:"container fade-in",children:(0,Ae.jsxs)("div",{className:"text-center",style:{padding:"3rem 1rem"},children:[(0,Ae.jsx)("div",{style:{fontSize:"4rem",marginBottom:"1rem",opacity:.3},children:"\ud83d\uded2"}),(0,Ae.jsx)("h3",{style:{color:"var(--text-light)"},children:"Carrello vuoto"}),(0,Ae.jsx)("p",{style:{color:"var(--text-light)",marginBottom:"2rem"},children:"Aggiungi alcuni prodotti prima di procedere al checkout"}),(0,Ae.jsxs)("a",{href:"/menu",className:"btn btn-primary",children:[(0,Ae.jsx)("i",{className:"fas fa-utensils"})," Vai al Menu"]})]})}):(0,Ae.jsxs)("div",{className:"container fade-in",children:[(0,Ae.jsxs)("h1",{className:"text-center mb-3",style:{color:"var(--primary-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-credit-card"})," Checkout"]}),(0,Ae.jsx)("form",{onSubmit:async e=>{if(e.preventDefault(),E(!0),T({name:!0,phone:!0,email:!0,address:!0}),(()=>{const e={};return e.name=L("name",s.name),e.phone=L("phone",s.phone),e.email=L("email",s.email),e.address=L("address",s.address),"scheduled"!==d||m||(e.scheduledTime="Seleziona un orario"),"card"===h&&(v.cardNumber.trim()?/^[0-9\s]{13,19}$/.test(v.cardNumber.replace(/\s/g,""))||(e.cardNumber="Numero carta non valido"):e.cardNumber="Numero carta richiesto",v.expiryDate.trim()?/^(0[1-9]|1[0-2])\/([0-9]{2})$/.test(v.expiryDate)||(e.expiryDate="Formato: MM/AA"):e.expiryDate="Data di scadenza richiesta",v.cvv.trim()?/^[0-9]{3,4}$/.test(v.cvv)||(e.cvv="CVV non valido"):e.cvv="CVV richiesto",v.cardholderName.trim()||(e.cardholderName="Nome titolare richiesto")),Object.keys(e).forEach((t=>{e[t]||delete e[t]})),z(e),0===Object.keys(e).length})()){if("card"===h&&b)return E(!1),void await(async()=>{try{S(!0);const e=await fetch(Me("STRIPE_CREATE_CUSTOMER"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:s.name,email:s.email||"".concat(s.phone,"@temp.com")})});if(!e.ok)throw new Error("Errore creazione customer");const n=await e.json(),r=t.map((e=>"".concat(e.quantity,"x ").concat(e.name))).join(", "),a=await fetch(Me("STRIPE_CREATE_PRODUCT"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:"Ordine Pizzeria Jasmin",description:r})});if(!a.ok)throw new Error("Errore creazione prodotto");const l=await a.json(),i=Math.round(100*R()),o=await fetch(Me("STRIPE_CREATE_PRICE"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({product:l.id,unit_amount:i,currency:"eur"})});if(!o.ok)throw new Error("Errore creazione prezzo");const c=await o.json(),u=await fetch(Me("STRIPE_CREATE_PAYMENT_LINK"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({price:c.id,quantity:1})});if(!u.ok)throw new Error("Errore creazione payment link");const d=await u.json();await F(n.id,l.id,c.id),window.location.href=d.url}catch(e){console.error("Errore Stripe:",e),z((e=>u(u({},e),{},{stripe:"Errore nel processamento del pagamento. Riprova."}))),S(!1)}})();try{const e={customer:{name:s.name,phone:s.phone,email:s.email||null},items:t.map((e=>({id:e.id,name:e.name,price:P(e),quantity:e.quantity,customizations:e.customizations||{}}))),total_amount:R(),delivery_type:i,delivery_address:"delivery"===i?"".concat(s.address,", ").concat(s.city):null,delivery_time:"asap"===d?"Il prima possibile":m,payment_method:h,card_data:"card"!==h||b?null:{card_number:v.cardNumber.replace(/\s/g,""),expiry_date:v.expiryDate,cvv:v.cvv,cardholder_name:v.cardholderName},notes:s.notes||null},r=await fetch(Me("ORDERS"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!r.ok){const e=await r.json();throw new Error(e.error||"Errore durante l'invio dell'ordine al server")}const o=await r.json(),c={id:o.order_number||Date.now().toString(),items:t,customer:s,orderType:i,deliveryTime:"asap"===d?"Il prima possibile":m,paymentMethod:h,subtotal:O(),deliveryFee:N,total:R(),status:"new",estimatedTime:I(),createdAt:(new Date).toISOString(),backendOrderId:o.id};if(!n(c))throw new Error("Errore durante il salvataggio dell'ordine locale");a(),localStorage.setItem("pizzeria-cart","[]"),l("/order-tracking?id=".concat(c.id))}catch(r){console.error("Errore durante l'invio dell'ordine:",r),z({submit:r.message||"Errore durante l'invio dell'ordine. Riprova."})}finally{E(!1)}}else{E(!1);const e=document.querySelector(".form-input.error");e&&(e.scrollIntoView({behavior:"smooth",block:"center"}),e.focus())}},children:(0,Ae.jsxs)("div",{className:"grid grid-2 gap-3",children:[(0,Ae.jsxs)("div",{children:[(0,Ae.jsx)("div",{className:"card mb-3",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("h3",{className:"mb-3",children:[(0,Ae.jsx)("i",{className:"fas fa-truck"})," Tipo di Ordine"]}),(0,Ae.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,Ae.jsxs)("button",{type:"button",onClick:()=>o("delivery"),className:"btn flex-1 ".concat("delivery"===i?"btn-primary":"btn-outline"),children:[(0,Ae.jsx)("i",{className:"fas fa-truck"})," Consegna a Domicilio"]}),(0,Ae.jsxs)("button",{type:"button",onClick:()=>o("pickup"),className:"btn flex-1 ".concat("pickup"===i?"btn-primary":"btn-outline"),children:[(0,Ae.jsx)("i",{className:"fas fa-store"})," Ritiro in Negozio"]})]}),"delivery"===i&&(0,Ae.jsx)("div",{className:"p-2",style:{background:"var(--background-light)",borderRadius:"var(--border-radius)"},children:(0,Ae.jsxs)("p",{style:{margin:0,fontSize:"0.9rem",color:"var(--text-light)"},children:[(0,Ae.jsx)("i",{className:"fas fa-info-circle"}),"Consegna gratuita a Missaglia per ordini superiori a \u20ac15"]})})]})}),(0,Ae.jsx)("div",{className:"card mb-3",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("h3",{className:"mb-3",children:[(0,Ae.jsx)("i",{className:"fas fa-user"})," Informazioni Cliente"]}),(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{className:"form-label",children:"Nome e Cognome *"}),(0,Ae.jsx)("input",{type:"text",className:"form-input ".concat(k.name?"error":""),value:s.name,onChange:e=>D("name",e.target.value),onBlur:()=>M("name"),placeholder:"Il tuo nome completo",autoComplete:"name",inputMode:"text"}),k.name&&(0,Ae.jsxs)("span",{className:"error-text",children:[(0,Ae.jsx)("i",{className:"fas fa-exclamation-circle"})," ",k.name]})]}),(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{className:"form-label",children:"Telefono *"}),(0,Ae.jsx)("input",{type:"tel",className:"form-input ".concat(k.phone?"error":""),value:s.phone,onChange:e=>D("phone",e.target.value),onBlur:()=>M("phone"),placeholder:"+39 ************",autoComplete:"tel",inputMode:"tel"}),k.phone&&(0,Ae.jsxs)("span",{className:"error-text",children:[(0,Ae.jsx)("i",{className:"fas fa-exclamation-circle"})," ",k.phone]})]}),(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{className:"form-label",children:"Email (opzionale)"}),(0,Ae.jsx)("input",{type:"email",className:"form-input ".concat(k.email?"error":""),value:s.email,onChange:e=>D("email",e.target.value),onBlur:()=>M("email"),placeholder:"<EMAIL>",autoComplete:"email",inputMode:"email"}),k.email&&(0,Ae.jsxs)("span",{className:"error-text",children:[(0,Ae.jsx)("i",{className:"fas fa-exclamation-circle"})," ",k.email]})]}),"delivery"===i&&(0,Ae.jsxs)(Ae.Fragment,{children:[(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{className:"form-label",children:"Citt\xe0 *"}),(0,Ae.jsxs)("select",{className:"form-select",value:s.city,onChange:e=>c(u(u({},s),{},{city:e.target.value})),children:[(0,Ae.jsx)("option",{value:"Missaglia",children:"Missaglia (Gratuita sopra \u20ac15)"}),(0,Ae.jsx)("option",{value:"Casatenovo",children:"Casatenovo (+\u20ac2.50)"}),(0,Ae.jsx)("option",{value:"Barzan\xf2",children:"Barzan\xf2 (+\u20ac3.00)"}),(0,Ae.jsx)("option",{value:"Cremella",children:"Cremella (+\u20ac3.50)"}),(0,Ae.jsx)("option",{value:"Monticello Brianza",children:"Monticello Brianza (+\u20ac4.00)"}),(0,Ae.jsx)("option",{value:"Vigan\xf2",children:"Vigan\xf2 (+\u20ac4.50)"}),(0,Ae.jsx)("option",{value:"Altro",children:"Altro (+\u20ac5.00)"})]})]}),(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{className:"form-label",children:"Indirizzo *"}),(0,Ae.jsx)("input",{type:"text",className:"form-input ".concat(k.address?"error":""),value:s.address,onChange:e=>D("address",e.target.value),onBlur:()=>M("address"),placeholder:"Via Roma 123, Scala A, Piano 2",autoComplete:"street-address",inputMode:"text"}),k.address&&(0,Ae.jsxs)("span",{className:"error-text",children:[(0,Ae.jsx)("i",{className:"fas fa-exclamation-circle"})," ",k.address]})]})]}),(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{className:"form-label",children:"Note aggiuntive"}),(0,Ae.jsx)("textarea",{className:"form-input",value:s.notes,onChange:e=>c(u(u({},s),{},{notes:e.target.value})),placeholder:"Citofono, allergie, richieste speciali...",rows:"3"})]})]})}),(0,Ae.jsx)("div",{className:"card mb-3",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("h3",{className:"mb-3",children:[(0,Ae.jsx)("i",{className:"fas fa-clock"})," Orario ","delivery"===i?"Consegna":"Ritiro"]}),(0,Ae.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,Ae.jsxs)("button",{type:"button",onClick:()=>f("asap"),className:"btn flex-1 ".concat("asap"===d?"btn-primary":"btn-outline"),children:[(0,Ae.jsx)("i",{className:"fas fa-bolt"})," Il Prima Possibile"]}),(0,Ae.jsxs)("button",{type:"button",onClick:()=>f("scheduled"),className:"btn flex-1 ".concat("scheduled"===d?"btn-primary":"btn-outline"),children:[(0,Ae.jsx)("i",{className:"fas fa-calendar"})," Programma Orario"]})]}),"asap"===d&&(0,Ae.jsx)("div",{className:"p-2",style:{background:"var(--background-light)",borderRadius:"var(--border-radius)"},children:(0,Ae.jsxs)("p",{style:{margin:0,fontSize:"0.9rem",color:"var(--text-light)"},children:[(0,Ae.jsx)("i",{className:"fas fa-info-circle"}),"Tempo stimato: ",I()+("delivery"===i?20:0)," minuti"]})}),"scheduled"===d&&(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{className:"form-label",children:"Seleziona Orario"}),(0,Ae.jsxs)("select",{className:"form-select",value:m,onChange:e=>p(e.target.value),required:!0,children:[(0,Ae.jsx)("option",{value:"",children:"Scegli un orario..."}),(()=>{const e=[],t=new Date,n=I()+("delivery"===i?20:0)+10,r=new Date(t.getTime()+6e4*n);for(let a=17;a<22;a++)for(let t=17===a?30:0;t<60;t+=15){const n=new Date;if(n.setHours(a,t,0,0),n>=r){const t=n.toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"});e.push({value:t,label:t})}}return e})().map((e=>(0,Ae.jsx)("option",{value:e.value,children:e.label},e.value)))]})]})]})}),(0,Ae.jsx)("div",{className:"card",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("h3",{className:"mb-3",children:[(0,Ae.jsx)("i",{className:"fas fa-credit-card"})," Metodo di Pagamento"]}),(0,Ae.jsxs)("div",{className:"flex flex-column gap-2",children:[(0,Ae.jsxs)("label",{className:"flex align-center gap-2",style:{cursor:"pointer"},children:[(0,Ae.jsx)("input",{type:"radio",name:"payment",value:"cash",checked:"cash"===h,onChange:e=>g(e.target.value)}),(0,Ae.jsx)("i",{className:"fas fa-money-bill-wave"}),(0,Ae.jsxs)("span",{children:["Contanti alla ","delivery"===i?"consegna":"cassa"]})]}),(0,Ae.jsxs)("label",{className:"flex align-center gap-2",style:{cursor:"pointer"},children:[(0,Ae.jsx)("input",{type:"radio",name:"payment",value:"card",checked:"card"===h,onChange:e=>g(e.target.value)}),(0,Ae.jsx)("i",{className:"fas fa-credit-card"}),(0,Ae.jsx)("span",{children:"Carta di credito/debito"})]})]}),"card"===h&&(0,Ae.jsxs)("div",{className:"mt-3 p-3",style:{background:"var(--background-light)",borderRadius:"var(--border-radius)",border:"1px solid var(--border-color)"},children:[(0,Ae.jsxs)("h4",{className:"mb-3",style:{fontSize:"1rem",color:"var(--text-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-lock"})," Dati Carta di Credito"]}),(0,Ae.jsxs)("div",{className:"mb-3 p-3",style:{background:"var(--primary-color)",borderRadius:"var(--border-radius)",color:"white"},children:[(0,Ae.jsx)("h5",{className:"mb-2",style:{fontSize:"0.9rem",fontWeight:"bold"},children:"Modalit\xe0 Pagamento Carta"}),(0,Ae.jsxs)("div",{className:"flex flex-column gap-2",children:[(0,Ae.jsxs)("label",{className:"flex align-center gap-2",style:{cursor:"pointer"},children:[(0,Ae.jsx)("input",{type:"radio",name:"cardMode",checked:b,onChange:()=>x(!0)}),(0,Ae.jsx)("span",{style:{fontSize:"0.85rem"},children:"\ud83d\udd12 Pagamento Sicuro con Stripe (Consigliato)"})]}),(0,Ae.jsxs)("label",{className:"flex align-center gap-2",style:{cursor:"pointer"},children:[(0,Ae.jsx)("input",{type:"radio",name:"cardMode",checked:!b,onChange:()=>x(!1)}),(0,Ae.jsx)("span",{style:{fontSize:"0.85rem"},children:"\ud83d\udcb3 Inserimento Manuale Carta"})]})]})]}),b?(0,Ae.jsxs)("div",{className:"p-3 text-center",style:{background:"var(--success-color)",borderRadius:"var(--border-radius)",color:"white"},children:[(0,Ae.jsxs)("div",{className:"flex align-center justify-center mb-2",children:[(0,Ae.jsx)("i",{className:"fas fa-shield-alt",style:{fontSize:"1.5rem",marginRight:"0.5rem"}}),(0,Ae.jsx)("span",{style:{fontSize:"1.1rem",fontWeight:"bold"},children:"Pagamento Sicuro con Stripe"})]}),(0,Ae.jsx)("p",{style:{fontSize:"0.85rem",margin:"0 0 1rem 0",opacity:.9},children:"Verrai reindirizzato alla pagina sicura di Stripe per completare il pagamento."}),j&&(0,Ae.jsxs)("div",{className:"flex align-center justify-center",children:[(0,Ae.jsx)("i",{className:"fas fa-spinner fa-spin",style:{marginRight:"0.5rem"}}),(0,Ae.jsx)("span",{style:{fontSize:"0.85rem"},children:"Preparazione pagamento..."})]}),k.stripe&&(0,Ae.jsx)("p",{style:{color:"#ffebee",fontSize:"0.85rem",marginTop:"0.5rem"},children:k.stripe})]}):(0,Ae.jsxs)("div",{children:[(0,Ae.jsx)("div",{className:"mb-3 p-2",style:{background:"#fff3cd",borderRadius:"var(--border-radius)",border:"1px solid #ffeaa7"},children:(0,Ae.jsx)("p",{style:{margin:0,fontSize:"0.85rem",color:"#856404"},children:"\u26a0\ufe0f Modalit\xe0 di sviluppo: I dati della carta non verranno elaborati realmente."})}),(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{className:"form-label",children:"Numero Carta *"}),(0,Ae.jsx)("input",{type:"text",className:"form-input ".concat(k.cardNumber?"error":""),value:v.cardNumber,onChange:e=>{let t=e.target.value.replace(/\D/g,"").replace(/(\d{4})(?=\d)/g,"$1 ");t.length<=19&&y(u(u({},v),{},{cardNumber:t}))},onBlur:()=>M("cardNumber",!0),placeholder:"1234 5678 9012 3456",maxLength:"19",autoComplete:"cc-number"}),k.cardNumber&&(0,Ae.jsxs)("span",{className:"error-text",children:[(0,Ae.jsx)("i",{className:"fas fa-exclamation-circle"})," ",k.cardNumber]})]}),(0,Ae.jsxs)("div",{className:"flex gap-2",children:[(0,Ae.jsxs)("div",{className:"form-group flex-1",children:[(0,Ae.jsx)("label",{className:"form-label",children:"Scadenza *"}),(0,Ae.jsx)("input",{type:"text",className:"form-input ".concat(k.expiryDate?"error":""),value:v.expiryDate,onChange:e=>{let t=e.target.value.replace(/\D/g,"");t.length>=2&&(t=t.substring(0,2)+"/"+t.substring(2,4)),t.length<=5&&y(u(u({},v),{},{expiryDate:t}))},onBlur:()=>M("expiryDate",!0),placeholder:"MM/AA",maxLength:"5",autoComplete:"cc-exp"}),k.expiryDate&&(0,Ae.jsxs)("span",{className:"error-text",children:[(0,Ae.jsx)("i",{className:"fas fa-exclamation-circle"})," ",k.expiryDate]})]}),(0,Ae.jsxs)("div",{className:"form-group flex-1",children:[(0,Ae.jsx)("label",{className:"form-label",children:"CVV *"}),(0,Ae.jsx)("input",{type:"text",className:"form-input ".concat(k.cvv?"error":""),value:v.cvv,onChange:e=>{let t=e.target.value.replace(/\D/g,"");t.length<=4&&y(u(u({},v),{},{cvv:t}))},onBlur:()=>M("cvv",!0),placeholder:"123",maxLength:"4",autoComplete:"cc-csc"}),k.cvv&&(0,Ae.jsxs)("span",{className:"error-text",children:[(0,Ae.jsx)("i",{className:"fas fa-exclamation-circle"})," ",k.cvv]})]})]}),(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{className:"form-label",children:"Nome Titolare *"}),(0,Ae.jsx)("input",{type:"text",className:"form-input ".concat(k.cardholderName?"error":""),value:v.cardholderName,onChange:e=>y(u(u({},v),{},{cardholderName:e.target.value.toUpperCase()})),onBlur:()=>M("cardholderName",!0),placeholder:"MARIO ROSSI",autoComplete:"cc-name",style:{textTransform:"uppercase"}}),k.cardholderName&&(0,Ae.jsxs)("span",{className:"error-text",children:[(0,Ae.jsx)("i",{className:"fas fa-exclamation-circle"})," ",k.cardholderName]})]}),(0,Ae.jsxs)("div",{className:"flex align-center gap-2 mt-2",style:{fontSize:"0.85rem",color:"var(--text-light)"},children:[(0,Ae.jsx)("i",{className:"fas fa-shield-alt",style:{color:"var(--success-color)"}}),(0,Ae.jsx)("span",{children:"I tuoi dati sono protetti con crittografia SSL"})]})]})]})]})})]}),(0,Ae.jsx)("div",{children:(0,Ae.jsx)("div",{className:"card sticky-top",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("h3",{className:"mb-3",children:[(0,Ae.jsx)("i",{className:"fas fa-receipt"})," Riepilogo Ordine"]}),(0,Ae.jsx)("div",{className:"mb-3",children:t.map(((e,t)=>(0,Ae.jsxs)("div",{className:"flex flex-between mb-2 pb-2",style:{borderBottom:"1px solid var(--border-color)"},children:[(0,Ae.jsxs)("div",{className:"flex-1",children:[(0,Ae.jsx)("div",{style:{fontWeight:"bold"},children:e.name}),(0,Ae.jsxs)("div",{style:{fontSize:"0.8rem",color:"var(--text-light)"},children:["Quantit\xe0: ",e.quantity]})]}),(0,Ae.jsxs)("div",{style:{fontWeight:"bold"},children:["\u20ac",(P(e)*e.quantity).toFixed(2)]})]},"".concat(e.id,"-").concat(t))))}),(0,Ae.jsxs)("div",{className:"mb-3",children:[(0,Ae.jsxs)("div",{className:"flex flex-between mb-1",children:[(0,Ae.jsx)("span",{children:"Subtotale:"}),(0,Ae.jsxs)("span",{children:["\u20ac",O().toFixed(2)]})]}),"delivery"===i&&(0,Ae.jsxs)("div",{className:"flex flex-between mb-1",children:[(0,Ae.jsxs)("span",{children:["Consegna (",s.city,"):"]}),(0,Ae.jsx)("span",{style:{color:0===N?"var(--success-color)":"inherit"},children:0===N?"Gratuita":"\u20ac".concat(N.toFixed(2))})]}),(0,Ae.jsx)("hr",{style:{margin:"1rem 0"}}),(0,Ae.jsxs)("div",{className:"flex flex-between",style:{fontSize:"1.2rem",fontWeight:"bold"},children:[(0,Ae.jsx)("span",{children:"Totale:"}),(0,Ae.jsxs)("span",{style:{color:"var(--secondary-color)"},children:["\u20ac",R().toFixed(2)]})]})]}),(0,Ae.jsxs)("div",{className:"p-3 mb-3",style:{background:"linear-gradient(135deg, var(--primary-color), var(--secondary-color))",borderRadius:"var(--border-radius)",color:"white",textAlign:"center"},children:[(0,Ae.jsxs)("div",{style:{fontSize:"1.1rem",fontWeight:"bold",marginBottom:"0.5rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-user-check"})," Checkout Ospite"]}),(0,Ae.jsx)("div",{style:{fontSize:"0.9rem",opacity:.9},children:"Nessuna registrazione richiesta \u2022 Checkout veloce"})]}),k.submit&&(0,Ae.jsxs)("div",{className:"p-2 mb-3",style:{background:"var(--error-color)",color:"white",borderRadius:"var(--border-radius)",textAlign:"center"},children:[(0,Ae.jsx)("i",{className:"fas fa-exclamation-triangle"})," ",k.submit]}),(0,Ae.jsx)("button",{type:"submit",disabled:C||j,className:"btn btn-primary w-full ".concat(C||j?"loading":""),style:{fontSize:"1.2rem",padding:"1.2rem",minHeight:"60px",fontWeight:"bold",boxShadow:"0 4px 12px rgba(0,0,0,0.15)",transition:"all 0.3s ease"},children:C?(0,Ae.jsxs)(Ae.Fragment,{children:[(0,Ae.jsx)("i",{className:"fas fa-spinner fa-spin"})," Elaborazione..."]}):j?(0,Ae.jsxs)(Ae.Fragment,{children:[(0,Ae.jsx)("i",{className:"fas fa-spinner fa-spin"})," Preparazione Stripe..."]}):"card"===h&&b?(0,Ae.jsxs)(Ae.Fragment,{children:[(0,Ae.jsx)("i",{className:"fas fa-credit-card"})," Paga con Stripe \u2022 \u20ac",R().toFixed(2)]}):(0,Ae.jsxs)(Ae.Fragment,{children:[(0,Ae.jsx)("i",{className:"fas fa-check-circle"})," Conferma Ordine \u2022 \u20ac",R().toFixed(2)]})}),(0,Ae.jsx)("div",{className:"text-center mt-2",children:(0,Ae.jsxs)("a",{href:"/cart",style:{color:"var(--primary-color)",textDecoration:"none",fontSize:"0.9rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-arrow-left"})," Torna al carrello"]})})]})})})]})})]})};const Qe=function(e){let{orders:t,updateOrderStatus:n,riders:a,assignRiderToOrder:l,completeDelivery:i,onBackToCustomer:o,onLogout:s}=e;const[c,d]=(0,r.useState)(new Date),[f,m]=(0,r.useState)("active"),[p,h]=(0,r.useState)(null),[g,v]=(0,r.useState)([]),[y,b]=(0,r.useState)(!1),[x,j]=(0,r.useState)(null),[S,N]=(0,r.useState)(0),[w,k]=(0,r.useState)(!0);(0,r.useEffect)((()=>{const e=setInterval((()=>{d(new Date)}),1e3);return()=>clearInterval(e)}),[]),(0,r.useEffect)((()=>{if(!t||0===t.length)return;const e=t.filter((e=>"completed"!==e.status&&"cancelled"!==e.status)).length;if(e>S&&S>0&&w){const e=new Audio("data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT");e.volume=.3,e.play().catch((e=>console.log("Audio non disponibile")))}N(e)}),[t,S,w]),(0,r.useEffect)((()=>{const e=e=>{if("INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName)switch(e.key){case"1":m("active");break;case"2":m("completed");break;case"3":m("all");break;case"s":case"S":k(!w)}};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)}),[w]),(0,r.useEffect)((()=>{if(!t)return;const e=new Date,n=[];t.forEach((t=>{if("pending"===t.status||"preparing"===t.status){const r=new Date(t.createdAt),a=Math.floor((e-r)/6e4),l=t.estimatedTime||15;a>=l&&"pickup"===t.orderType?n.push({id:t.id,type:"ready_pickup",message:"Ordine #".concat(t.id.slice(-6)," pronto per il ritiro!"),time:e}):a>=l&&"delivery"===t.orderType&&n.push({id:t.id,type:"ready_delivery",message:"Ordine #".concat(t.id.slice(-6)," pronto per la consegna!"),time:e})}})),v(n)}),[t,c]);const z=e=>{const t=new Date,n=new Date(e.createdAt),r=Math.floor((t-n)/6e4),a=e.estimatedTime||15;if("cancelled"===e.status)return{status:"cancelled",label:"Annullato",color:"var(--danger-color)",priority:0,urgent:!1};if("completed"===e.status)return{status:"completed",label:"delivery"===e.orderType?"Consegnato":"Ritirato",color:"var(--success-color)",priority:0,urgent:!1};if("preparing"===e.status){const e=Math.max(0,a-r);return{status:"preparing",label:e>0?"In Preparazione (".concat(e,"min)"):"Quasi Pronto",color:"var(--primary-color)",priority:e<=2?4:3,urgent:e<=2}}if("new"===e.status)return{status:"new",label:r<2?"Nuovo Ordine":"Nuovo Ordine (".concat(r,"min fa)"),color:"var(--warning-color)",priority:5,urgent:r>5};if(r<2)return{status:"new",label:"Nuovo Ordine",color:"var(--warning-color)",priority:5,urgent:!0};if(r<a){const e=a-r;return{status:"preparing",label:"In Preparazione (".concat(e,"min)"),color:"var(--primary-color)",priority:e<=2?4:3,urgent:e<=2}}return"pickup"===e.orderType?{status:"ready_pickup",label:"Pronto per Ritiro",color:"var(--success-color)",priority:2,urgent:!0}:{status:"ready_delivery",label:"Pronto per Consegna",color:"var(--secondary-color)",priority:2,urgent:!0}},C=(e,t)=>{n&&n(e,t),h(null)},E=e=>{i&&i(e)},_=(()=>{if(!t)return{active:0,completed:0,revenue:0,avgTime:0};const e=new Date;e.setHours(0,0,0,0);const n=t.filter((t=>new Date(t.createdAt)>=e)),r=n.filter((e=>"completed"!==e.status&&"cancelled"!==e.status)).length,a=n.filter((e=>"completed"===e.status)).length;return{active:r,completed:a,revenue:n.filter((e=>"completed"===e.status)).reduce(((e,t)=>e+t.total),0),avgTime:a>0?n.filter((e=>"completed"===e.status)).reduce(((e,t)=>e+(t.estimatedTime||15)),0)/a:0}})(),T=(()=>{if(!t)return[];let e=t;switch(f){case"active":e=t.filter((e=>"completed"!==e.status&&"cancelled"!==e.status));break;case"completed":e=t.filter((e=>"completed"===e.status||"cancelled"===e.status));break;default:e=t}return e.sort(((e,t)=>{const n=z(e),r=z(t);return n.priority!==r.priority?r.priority-n.priority:new Date(e.createdAt)-new Date(t.createdAt)}))})(),P=T.filter((e=>z(e).urgent));return(0,Ae.jsxs)("div",{className:"container fade-in",children:[(0,Ae.jsxs)("div",{className:"flex flex-between align-center mb-3",children:[(0,Ae.jsxs)("div",{children:[(0,Ae.jsxs)("h1",{style:{color:"var(--primary-color)",margin:0},children:[(0,Ae.jsx)("i",{className:"fas fa-fire"})," Dashboard Cucina"]}),(0,Ae.jsx)("div",{style:{fontSize:"0.9rem",color:"var(--text-light)"},children:"Pizzeria Jasmin - Missaglia"})]}),(0,Ae.jsxs)("div",{className:"flex align-center gap-2",children:[(0,Ae.jsxs)("div",{style:{fontSize:"1.2rem",color:"var(--text-color)",background:"var(--primary-color)10",padding:"0.5rem 1rem",borderRadius:"var(--border-radius)",border:"1px solid var(--primary-color)30"},children:[(0,Ae.jsx)("i",{className:"fas fa-clock"})," ",c.toLocaleTimeString("it-IT")]}),(0,Ae.jsx)("button",{onClick:()=>k(!w),className:"btn ".concat(w?"btn-primary":"btn-outline"),style:{fontSize:"0.9rem"},title:w?"Disattiva suoni":"Attiva suoni",children:(0,Ae.jsx)("i",{className:"fas ".concat(w?"fa-volume-up":"fa-volume-mute")})}),g.length>0&&(0,Ae.jsxs)("div",{className:"status-badge",style:{background:"var(--danger-color)",color:"white",animation:"pulse 2s infinite"},children:[(0,Ae.jsx)("i",{className:"fas fa-bell"})," ",g.length]}),o&&(0,Ae.jsxs)("button",{onClick:o,className:"btn btn-outline",style:{fontSize:"0.9rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-arrow-left"})," Sito"]}),s&&(0,Ae.jsxs)("button",{onClick:s,className:"btn btn-danger",style:{fontSize:"0.9rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-sign-out-alt"})," Logout"]})]})]}),(0,Ae.jsxs)("div",{className:"grid grid-4 gap-2 mb-3",children:[(0,Ae.jsx)("div",{className:"card text-center",style:{background:_.active>5?"var(--warning-color)10":"white",border:_.active>5?"2px solid var(--warning-color)":"1px solid var(--border-color)"},children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsx)("div",{style:{fontSize:"2.5rem",color:_.active>5?"var(--danger-color)":"var(--warning-color)",marginBottom:"0.5rem",fontWeight:"bold"},children:_.active}),(0,Ae.jsxs)("div",{style:{fontSize:"0.9rem",color:"var(--text-light)"},children:["Ordini Attivi",_.active>5&&(0,Ae.jsx)("div",{style:{color:"var(--danger-color)",fontSize:"0.8rem"},children:"\u26a0\ufe0f Molti ordini!"})]})]})}),(0,Ae.jsx)("div",{className:"card text-center",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsx)("div",{style:{fontSize:"2.5rem",color:"var(--success-color)",marginBottom:"0.5rem",fontWeight:"bold"},children:_.completed}),(0,Ae.jsxs)("div",{style:{fontSize:"0.9rem",color:"var(--text-light)"},children:["Completati Oggi",(0,Ae.jsx)("div",{style:{fontSize:"0.8rem",color:"var(--success-color)"},children:_.completed>0?"\ud83c\udfaf ".concat((_.completed/(_.completed+_.active)*100).toFixed(0),"% completati"):""})]})]})}),(0,Ae.jsx)("div",{className:"card text-center",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("div",{style:{fontSize:"2.5rem",color:"var(--secondary-color)",marginBottom:"0.5rem",fontWeight:"bold"},children:["\u20ac",_.revenue.toFixed(0)]}),(0,Ae.jsxs)("div",{style:{fontSize:"0.9rem",color:"var(--text-light)"},children:["Incasso Oggi",(0,Ae.jsx)("div",{style:{fontSize:"0.8rem",color:"var(--secondary-color)"},children:_.completed>0?"\ud83d\udcb0 \u20ac".concat((_.revenue/_.completed).toFixed(1)," medio"):""})]})]})}),(0,Ae.jsx)("div",{className:"card text-center",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("div",{style:{fontSize:"2.5rem",color:_.avgTime>20?"var(--danger-color)":"var(--primary-color)",marginBottom:"0.5rem",fontWeight:"bold"},children:[_.avgTime.toFixed(0),"min"]}),(0,Ae.jsxs)("div",{style:{fontSize:"0.9rem",color:"var(--text-light)"},children:["Tempo Medio",(0,Ae.jsx)("div",{style:{fontSize:"0.8rem",color:_.avgTime>20?"var(--danger-color)":"var(--primary-color)"},children:_.avgTime>20?"\u23f0 Troppo lento!":"\u26a1 Buon ritmo!"})]})]})})]}),a&&a.length>0&&(0,Ae.jsx)("div",{className:"card mb-3",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("h3",{style:{marginBottom:"1rem",color:"var(--primary-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-motorcycle"})," Stato Rider"]}),(0,Ae.jsx)("div",{className:"grid grid-2 gap-2",children:a.map((e=>{const n=e.currentOrder?t.find((t=>t.id===e.currentOrder)):null;return(0,Ae.jsxs)("div",{className:"flex flex-between align-center p-2",style:{background:"available"===e.status?"var(--success-color)20":"var(--warning-color)20",borderRadius:"var(--border-radius)",border:"1px solid ".concat("available"===e.status?"var(--success-color)":"var(--warning-color)")},children:[(0,Ae.jsxs)("div",{children:[(0,Ae.jsxs)("div",{style:{fontWeight:"bold"},children:[(0,Ae.jsx)("i",{className:"fas fa-user"})," ",e.name]}),(0,Ae.jsxs)("div",{style:{fontSize:"0.8rem",color:"var(--text-light)"},children:[(0,Ae.jsx)("i",{className:"fas fa-phone"})," ",e.phone]}),n&&(0,Ae.jsxs)("div",{style:{fontSize:"0.8rem",color:"var(--primary-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-box"})," Ordine #",n.id.slice(-6)]})]}),(0,Ae.jsxs)("div",{className:"text-center",children:[(0,Ae.jsx)("span",{className:"status-badge",style:{background:"available"===e.status?"var(--success-color)":"var(--warning-color)",color:"white"},children:"available"===e.status?"Disponibile":"In Consegna"}),n&&(0,Ae.jsx)("div",{style:{marginTop:"0.5rem"},children:(0,Ae.jsxs)("button",{onClick:()=>E(n.id),className:"btn btn-success",style:{fontSize:"0.8rem",padding:"0.25rem 0.5rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-check"})," Completata"]})})]})]},e.id)}))})]})}),P.length>0&&(0,Ae.jsx)("div",{className:"card mb-3",style:{background:"var(--danger-color)",color:"white"},children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("h3",{className:"mb-2",children:[(0,Ae.jsx)("i",{className:"fas fa-exclamation-triangle"})," Attenzione!"]}),(0,Ae.jsxs)("p",{style:{margin:0},children:[P.length," ",1===P.length?"ordine richiede":"ordini richiedono"," attenzione immediata"]})]})}),(0,Ae.jsxs)("div",{className:"flex flex-between align-center mb-3",children:[(0,Ae.jsxs)("div",{className:"flex gap-2",children:[(0,Ae.jsxs)("button",{onClick:()=>m("active"),className:"btn ".concat("active"===f?"btn-primary":"btn-outline"),children:[(0,Ae.jsx)("i",{className:"fas fa-fire"})," Attivi (",(null===t||void 0===t?void 0:t.filter((e=>"completed"!==e.status&&"cancelled"!==e.status)).length)||0,")",(0,Ae.jsx)("span",{style:{fontSize:"0.8rem",opacity:.7},children:" [1]"})]}),(0,Ae.jsxs)("button",{onClick:()=>m("completed"),className:"btn ".concat("completed"===f?"btn-primary":"btn-outline"),children:[(0,Ae.jsx)("i",{className:"fas fa-check"})," Completati",(0,Ae.jsx)("span",{style:{fontSize:"0.8rem",opacity:.7},children:" [2]"})]}),(0,Ae.jsxs)("button",{onClick:()=>m("all"),className:"btn ".concat("all"===f?"btn-primary":"btn-outline"),children:[(0,Ae.jsx)("i",{className:"fas fa-list"})," Tutti",(0,Ae.jsx)("span",{style:{fontSize:"0.8rem",opacity:.7},children:" [3]"})]})]}),(0,Ae.jsxs)("div",{style:{fontSize:"0.8rem",color:"var(--text-light)"},children:[(0,Ae.jsx)("i",{className:"fas fa-keyboard"})," Scorciatoie: 1-3 (filtri), S (suoni)"]})]}),0===T.length?(0,Ae.jsxs)("div",{className:"text-center",style:{padding:"3rem 1rem"},children:[(0,Ae.jsx)("div",{style:{fontSize:"4rem",marginBottom:"1rem",opacity:.3},children:"\ud83c\udf55"}),(0,Ae.jsx)("h3",{style:{color:"var(--text-light)"},children:"Nessun ordine"}),(0,Ae.jsx)("p",{style:{color:"var(--text-light)"},children:"active"===f?"Non ci sono ordini attivi al momento":"Nessun ordine trovato"})]}):(0,Ae.jsx)("div",{className:"grid grid-1 gap-3",children:T.map((e=>{var t;const n=z(e);return(0,Ae.jsx)("div",{className:"card ".concat(n.urgent?"urgent-order":""),style:u({borderLeft:"4px solid ".concat(n.color)},n.urgent&&{animation:"pulse-border 2s infinite",boxShadow:"0 0 10px ".concat(n.color,"30")}),children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("div",{className:"flex flex-between align-center mb-3",children:[(0,Ae.jsxs)("div",{children:[(0,Ae.jsxs)("h3",{style:{margin:0,color:"var(--primary-color)",fontSize:n.urgent?"1.3rem":"1.1rem"},children:[n.urgent&&(0,Ae.jsx)("span",{style:{marginRight:"0.5rem",fontSize:"1.2rem"},children:"\ud83d\udd25"}),"Ordine #",e.id.slice(-6),"delivery"===e.orderType&&(0,Ae.jsx)("span",{style:{marginLeft:"0.5rem",fontSize:"0.9rem"},children:"\ud83d\ude9a"}),"pickup"===e.orderType&&(0,Ae.jsx)("span",{style:{marginLeft:"0.5rem",fontSize:"0.9rem"},children:"\ud83c\udfea"})]}),(0,Ae.jsxs)("div",{style:{fontSize:"0.9rem",color:"var(--text-light)"},children:[(l=e.createdAt,new Date(l).toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"}))," \u2022 ",e.customer.name," \u2022",(0,Ae.jsx)("span",{style:{color:n.color,fontWeight:"bold"},children:n.label}),e.riderId&&a&&(0,Ae.jsxs)("span",{style:{marginLeft:"0.5rem"},children:["\u2022 ",(0,Ae.jsx)("i",{className:"fas fa-motorcycle"})," ",null===(t=a.find((t=>t.id===e.riderId)))||void 0===t?void 0:t.name]})]})]}),(0,Ae.jsxs)("div",{className:"flex align-center gap-2",children:[(0,Ae.jsxs)("span",{className:"status-badge",style:{background:n.color,color:"white"},children:[(0,Ae.jsx)("i",{className:"delivery"===e.orderType?"fas fa-truck":"fas fa-store"}),"delivery"===e.orderType?"Consegna":"Ritiro"]}),(0,Ae.jsxs)("span",{className:"status-badge",style:{background:"var(--secondary-color)",color:"white"},children:["\u20ac",e.total.toFixed(2)]})]})]}),(0,Ae.jsxs)("div",{className:"grid grid-2 gap-3 mb-3",children:[(0,Ae.jsxs)("div",{children:[(0,Ae.jsxs)("h4",{style:{marginBottom:"0.5rem",color:"var(--text-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-utensils"})," Prodotti"]}),e.items.map(((e,t)=>(0,Ae.jsxs)("div",{className:"flex flex-between mb-1",style:{fontSize:"0.9rem"},children:[(0,Ae.jsxs)("span",{children:[e.quantity,"x ",e.name,e.customizations&&Object.keys(e.customizations).length>0&&(0,Ae.jsxs)("span",{style:{color:"var(--text-light)",fontSize:"0.8rem"},children:[" ","(",Object.entries(e.customizations).map((e=>{let[t,n]=e;return n})).join(", "),")"]})]}),(0,Ae.jsxs)("span",{style:{fontWeight:"bold"},children:["\u23f1\ufe0f ",e.preparationTime*e.quantity,"min"]})]},t)))]}),(0,Ae.jsxs)("div",{children:[(0,Ae.jsxs)("h4",{style:{marginBottom:"0.5rem",color:"var(--text-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-user"})," Cliente"]}),(0,Ae.jsxs)("div",{style:{fontSize:"0.9rem"},children:[(0,Ae.jsx)("div",{children:(0,Ae.jsx)("strong",{children:e.customer.name})}),(0,Ae.jsxs)("div",{children:[(0,Ae.jsx)("i",{className:"fas fa-phone"})," ",e.customer.phone,(0,Ae.jsx)("a",{href:"tel:".concat(e.customer.phone),style:{marginLeft:"0.5rem",color:"var(--primary-color)"},children:(0,Ae.jsx)("i",{className:"fas fa-phone-alt"})})]}),"delivery"===e.orderType&&(0,Ae.jsx)(Ae.Fragment,{children:(0,Ae.jsxs)("div",{style:{background:"var(--primary-color)10",padding:"0.5rem",borderRadius:"var(--border-radius)",marginTop:"0.5rem",border:"1px solid var(--primary-color)30"},children:[(0,Ae.jsxs)("div",{style:{fontWeight:"bold",color:"var(--primary-color)",marginBottom:"0.25rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-map-marker-alt"})," Indirizzo Consegna"]}),(0,Ae.jsx)("div",{style:{marginBottom:"0.25rem"},children:e.customer.address}),(0,Ae.jsxs)("div",{style:{marginBottom:"0.5rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-city"})," ",e.customer.city,(0,Ae.jsxs)("span",{style:{color:"var(--text-light)",marginLeft:"0.5rem"},children:["(~",(r=e.customer.city,{Missaglia:"0 km",Casatenovo:"3 km","Barzan\xf2":"5 km",Cremella:"7 km","Monticello Brianza":"8 km","Vigan\xf2":"10 km"}[r]||"~12 km"),")"]})]}),(0,Ae.jsxs)("button",{onClick:()=>(e=>{const t="".concat(e.customer.address,", ").concat(e.customer.city,", Italy"),n=encodeURIComponent(t),r="https://www.google.com/maps/dir/?api=1&destination=".concat(n);window.open(r,"_blank"),console.log("Navigazione aperta per: ".concat(t))})(e),className:"btn btn-primary",style:{fontSize:"0.8rem",padding:"0.25rem 0.5rem",width:"100%"},children:[(0,Ae.jsx)("i",{className:"fas fa-route"})," Naviga con Google Maps"]})]})}),e.customer.notes&&(0,Ae.jsxs)("div",{style:{color:"var(--warning-color)",fontStyle:"italic",background:"var(--warning-color)10",padding:"0.5rem",borderRadius:"var(--border-radius)",marginTop:"0.5rem",border:"1px solid var(--warning-color)30"},children:[(0,Ae.jsx)("i",{className:"fas fa-sticky-note"})," ",(0,Ae.jsx)("strong",{children:"Note:"})," ",e.customer.notes]})]})]})]}),"completed"!==e.status&&"cancelled"!==e.status&&(0,Ae.jsxs)("div",{className:"flex gap-2",children:["new"===n.status&&(0,Ae.jsxs)("button",{onClick:()=>C(e.id,"preparing"),className:"btn btn-primary",children:[(0,Ae.jsx)("i",{className:"fas fa-play"})," Inizia Preparazione"]}),("preparing"===n.status||"ready_pickup"===n.status||"ready_delivery"===n.status)&&(0,Ae.jsx)(Ae.Fragment,{children:"pickup"===e.orderType?(0,Ae.jsxs)("button",{onClick:()=>C(e.id,"completed"),className:"btn btn-success",children:[(0,Ae.jsx)("i",{className:"fas fa-check"})," Ritirato"]}):(0,Ae.jsx)(Ae.Fragment,{children:"out_for_delivery"===e.status?(0,Ae.jsxs)("button",{onClick:()=>E(e.id),className:"btn btn-success",children:[(0,Ae.jsx)("i",{className:"fas fa-check"})," Consegnato"]}):e.riderId?(0,Ae.jsxs)("span",{className:"status-badge",style:{background:"var(--info-color)",color:"white"},children:[(0,Ae.jsx)("i",{className:"fas fa-motorcycle"})," In Consegna"]}):(0,Ae.jsxs)("button",{onClick:()=>{return t=e.id,j(t),void b(!0);var t},className:"btn btn-info",children:[(0,Ae.jsx)("i",{className:"fas fa-motorcycle"})," Assegna Rider"]})})}),(0,Ae.jsxs)("button",{onClick:()=>C(e.id,"cancelled"),className:"btn btn-danger",children:[(0,Ae.jsx)("i",{className:"fas fa-times"})," Annulla"]}),(0,Ae.jsxs)("a",{href:"tel:".concat(e.customer.phone),className:"btn btn-outline",children:[(0,Ae.jsx)("i",{className:"fas fa-phone"})," Chiama"]})]})]})},e.id);var r,l}))}),y&&(0,Ae.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,background:"rgba(0,0,0,0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:(0,Ae.jsxs)("div",{style:{background:"white",padding:"2rem",borderRadius:"var(--border-radius)",maxWidth:"400px",width:"90%"},children:[(0,Ae.jsxs)("h3",{style:{marginBottom:"1rem",color:"var(--primary-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-motorcycle"})," Seleziona Rider"]}),(0,Ae.jsxs)("div",{style:{marginBottom:"1rem"},children:[null===a||void 0===a?void 0:a.filter((e=>"available"===e.status)).map((e=>(0,Ae.jsxs)("div",{onClick:()=>{return t=e.id,l&&x&&l(x,t),b(!1),void j(null);var t},style:{padding:"1rem",border:"1px solid var(--border-color)",borderRadius:"var(--border-radius)",marginBottom:"0.5rem",cursor:"pointer",transition:"all 0.2s"},onMouseOver:e=>e.target.style.background="var(--primary-color)10",onMouseOut:e=>e.target.style.background="white",children:[(0,Ae.jsxs)("div",{style:{fontWeight:"bold"},children:[(0,Ae.jsx)("i",{className:"fas fa-user"})," ",e.name]}),(0,Ae.jsxs)("div",{style:{fontSize:"0.9rem",color:"var(--text-light)"},children:[(0,Ae.jsx)("i",{className:"fas fa-phone"})," ",e.phone]}),(0,Ae.jsxs)("div",{style:{fontSize:"0.8rem",color:"var(--success-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-check-circle"})," Disponibile"]})]},e.id))),0===(null===a||void 0===a?void 0:a.filter((e=>"available"===e.status)).length)&&(0,Ae.jsxs)("div",{style:{textAlign:"center",color:"var(--text-light)",padding:"2rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-exclamation-triangle"}),(0,Ae.jsx)("br",{}),"Nessun rider disponibile al momento"]})]}),(0,Ae.jsx)("div",{style:{display:"flex",gap:"1rem"},children:(0,Ae.jsx)("button",{onClick:()=>b(!1),className:"btn btn-outline",style:{flex:1},children:"Annulla"})})]})})]})};if("undefined"!==typeof document){const e=document.createElement("style");e.textContent="\n@keyframes pulse-border {\n  0% { box-shadow: 0 0 10px rgba(255, 0, 0, 0.3); }\n  50% { box-shadow: 0 0 20px rgba(255, 0, 0, 0.6); }\n  100% { box-shadow: 0 0 10px rgba(255, 0, 0, 0.3); }\n}\n\n.urgent-order {\n  position: relative;\n}\n\n.urgent-order::before {\n  content: '';\n  position: absolute;\n  top: -2px;\n  left: -2px;\n  right: -2px;\n  bottom: -2px;\n  background: linear-gradient(45deg, #ff0000, #ff6600, #ff0000);\n  border-radius: var(--border-radius);\n  z-index: -1;\n  opacity: 0.1;\n  animation: pulse-bg 2s infinite;\n}\n\n@keyframes pulse-bg {\n  0%, 100% { opacity: 0.1; }\n  50% { opacity: 0.3; }\n}\n\n.grid-4 {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 1rem;\n}\n\n@media (max-width: 768px) {\n  .grid-4 {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n",document.head.appendChild(e)}const qe=function(e){let{orders:t}=e;const[n,a]=(0,r.useState)(null);(0,r.useEffect)((()=>{const e=new URLSearchParams(window.location.search).get("id");if(e&&t){const n=t.find((t=>t.id===e));n&&a(n)}}),[t]);const l=e=>{const t=new Date,n=new Date(e.createdAt),r=Math.floor((t-n)/6e4);if("cancelled"===e.status)return{status:"cancelled",label:"Annullato",color:"var(--danger-color)",icon:"fas fa-times-circle",progress:0};if("completed"===e.status)return{status:"completed",label:"delivery"===e.orderType?"Consegnato":"Ritirato",color:"var(--success-color)",icon:"fas fa-check-circle",progress:100};const a=e.estimatedTime||15,l="delivery"===e.orderType?20:0,i=a+l;return r<2?{status:"received",label:"Ordine Ricevuto",color:"var(--warning-color)",icon:"fas fa-receipt",progress:10}:r<a?{status:"preparing",label:"In Preparazione",color:"var(--primary-color)",icon:"fas fa-fire",progress:10+r/a*60}:"pickup"===e.orderType?{status:"ready",label:"Pronto per il Ritiro",color:"var(--success-color)",icon:"fas fa-bell",progress:90}:r<i?{status:"delivering",label:"In Consegna",color:"var(--secondary-color)",icon:"fas fa-truck",progress:70+(r-a)/l*20}:{status:"ready",label:"In Arrivo",color:"var(--success-color)",icon:"fas fa-map-marker-alt",progress:95}},i=e=>{const t=new Date(e.createdAt),n=(e.estimatedTime||15)+("delivery"===e.orderType?20:0);return new Date(t.getTime()+6e4*n).toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"})},o=e=>{const t=new Date,n=new Date(e.createdAt),r=(e.estimatedTime||15)+("delivery"===e.orderType?20:0),a=new Date(n.getTime()+6e4*r),l=Math.max(0,Math.floor((a-t)/6e4));return 0===l?"delivery"===e.orderType?"In arrivo":"Pronto":"".concat(l," min")},s=e=>new Date(e).toLocaleString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"});if(!t||0===t.length)return(0,Ae.jsxs)("div",{className:"container fade-in",children:[(0,Ae.jsxs)("h1",{className:"text-center mb-3",style:{color:"var(--primary-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-search"})," Tracking Ordini"]}),(0,Ae.jsxs)("div",{className:"text-center",style:{padding:"3rem 1rem"},children:[(0,Ae.jsx)("div",{style:{fontSize:"4rem",marginBottom:"1rem",opacity:.3},children:"\ud83d\udccb"}),(0,Ae.jsx)("h3",{style:{color:"var(--text-light)",marginBottom:"1rem"},children:"Nessun ordine trovato"}),(0,Ae.jsx)("p",{style:{color:"var(--text-light)",marginBottom:"2rem"},children:"Non hai ancora effettuato nessun ordine"}),(0,Ae.jsxs)("a",{href:"/menu",className:"btn btn-primary",children:[(0,Ae.jsx)("i",{className:"fas fa-utensils"})," Ordina Ora"]})]})]});if(n){const e=l(n);return(0,Ae.jsxs)("div",{className:"container fade-in",children:[(0,Ae.jsxs)("div",{className:"flex align-center gap-2 mb-3",children:[(0,Ae.jsx)("button",{onClick:()=>a(null),className:"btn btn-outline",children:(0,Ae.jsx)("i",{className:"fas fa-arrow-left"})}),(0,Ae.jsxs)("h1",{style:{color:"var(--primary-color)",margin:0},children:["Ordine #",n.id.slice(-6)]})]}),(0,Ae.jsx)("div",{className:"card mb-3",style:{background:"linear-gradient(135deg, ".concat(e.color,"15, ").concat(e.color,"05)")},children:(0,Ae.jsxs)("div",{className:"card-body text-center",children:[(0,Ae.jsx)("div",{style:{fontSize:"3rem",color:e.color,marginBottom:"1rem"},children:(0,Ae.jsx)("i",{className:e.icon})}),(0,Ae.jsx)("h2",{style:{color:e.color,marginBottom:"0.5rem"},children:e.label}),(0,Ae.jsxs)("p",{style:{fontSize:"1.1rem",color:"var(--text-light)",marginBottom:"1rem"},children:["delivery"===n.orderType?"Consegna":"Ritiro"," stimato: ",i(n)]}),(0,Ae.jsx)("div",{style:{width:"100%",height:"8px",background:"var(--background-light)",borderRadius:"4px",overflow:"hidden",marginBottom:"1rem"},children:(0,Ae.jsx)("div",{style:{width:"".concat(e.progress,"%"),height:"100%",background:e.color,transition:"width 0.3s ease"}})}),(0,Ae.jsx)("div",{style:{fontSize:"1.2rem",fontWeight:"bold",color:e.color},children:o(n)})]})}),(0,Ae.jsx)("div",{className:"card mb-3",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("h3",{className:"mb-3",children:[(0,Ae.jsx)("i",{className:"fas fa-list"})," Stato dell'Ordine"]}),(0,Ae.jsxs)("div",{className:"timeline",children:[(0,Ae.jsxs)("div",{className:"timeline-item ".concat(e.progress>=10?"completed":"pending"),children:[(0,Ae.jsx)("div",{className:"timeline-icon",children:(0,Ae.jsx)("i",{className:"fas fa-receipt"})}),(0,Ae.jsxs)("div",{className:"timeline-content",children:[(0,Ae.jsx)("h4",{children:"Ordine Ricevuto"}),(0,Ae.jsx)("p",{children:"Il tuo ordine \xe8 stato confermato"}),(0,Ae.jsx)("small",{children:s(n.createdAt)})]})]}),(0,Ae.jsxs)("div",{className:"timeline-item ".concat(e.progress>=40?"completed":e.progress>=10?"active":"pending"),children:[(0,Ae.jsx)("div",{className:"timeline-icon",children:(0,Ae.jsx)("i",{className:"fas fa-fire"})}),(0,Ae.jsxs)("div",{className:"timeline-content",children:[(0,Ae.jsx)("h4",{children:"In Preparazione"}),(0,Ae.jsx)("p",{children:"I nostri chef stanno preparando il tuo ordine"})]})]}),"delivery"===n.orderType?(0,Ae.jsxs)(Ae.Fragment,{children:[(0,Ae.jsxs)("div",{className:"timeline-item ".concat(e.progress>=80?"completed":e.progress>=70?"active":"pending"),children:[(0,Ae.jsx)("div",{className:"timeline-icon",children:(0,Ae.jsx)("i",{className:"fas fa-truck"})}),(0,Ae.jsxs)("div",{className:"timeline-content",children:[(0,Ae.jsx)("h4",{children:"In Consegna"}),(0,Ae.jsx)("p",{children:"Il tuo ordine \xe8 in viaggio verso di te"})]})]}),(0,Ae.jsxs)("div",{className:"timeline-item ".concat(e.progress>=100?"completed":"pending"),children:[(0,Ae.jsx)("div",{className:"timeline-icon",children:(0,Ae.jsx)("i",{className:"fas fa-home"})}),(0,Ae.jsxs)("div",{className:"timeline-content",children:[(0,Ae.jsx)("h4",{children:"Consegnato"}),(0,Ae.jsx)("p",{children:"Il tuo ordine \xe8 stato consegnato"})]})]})]}):(0,Ae.jsxs)("div",{className:"timeline-item ".concat(e.progress>=90?"completed":"pending"),children:[(0,Ae.jsx)("div",{className:"timeline-icon",children:(0,Ae.jsx)("i",{className:"fas fa-bell"})}),(0,Ae.jsxs)("div",{className:"timeline-content",children:[(0,Ae.jsx)("h4",{children:"Pronto per il Ritiro"}),(0,Ae.jsx)("p",{children:"Il tuo ordine \xe8 pronto, puoi venire a ritirarlo"})]})]})]})]})}),(0,Ae.jsxs)("div",{className:"grid grid-2 gap-3",children:[(0,Ae.jsx)("div",{className:"card",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("h3",{className:"mb-3",children:[(0,Ae.jsx)("i",{className:"fas fa-user"})," Informazioni Cliente"]}),(0,Ae.jsxs)("div",{className:"mb-2",children:[(0,Ae.jsx)("strong",{children:"Nome:"})," ",n.customer.name]}),(0,Ae.jsxs)("div",{className:"mb-2",children:[(0,Ae.jsx)("strong",{children:"Telefono:"})," ",n.customer.phone]}),"delivery"===n.orderType&&(0,Ae.jsxs)(Ae.Fragment,{children:[(0,Ae.jsxs)("div",{className:"mb-2",children:[(0,Ae.jsx)("strong",{children:"Indirizzo:"})," ",n.customer.address]}),(0,Ae.jsxs)("div",{className:"mb-2",children:[(0,Ae.jsx)("strong",{children:"Citt\xe0:"})," ",n.customer.city]})]}),n.customer.notes&&(0,Ae.jsxs)("div",{className:"mb-2",children:[(0,Ae.jsx)("strong",{children:"Note:"})," ",n.customer.notes]})]})}),(0,Ae.jsx)("div",{className:"card",children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("h3",{className:"mb-3",children:[(0,Ae.jsx)("i",{className:"fas fa-receipt"})," Dettagli Ordine"]}),n.items.map(((e,t)=>(0,Ae.jsxs)("div",{className:"flex flex-between mb-2 pb-2",style:{borderBottom:"1px solid var(--border-color)"},children:[(0,Ae.jsxs)("div",{children:[(0,Ae.jsx)("div",{style:{fontWeight:"bold"},children:e.name}),(0,Ae.jsxs)("div",{style:{fontSize:"0.8rem",color:"var(--text-light)"},children:["Quantit\xe0: ",e.quantity]})]}),(0,Ae.jsxs)("div",{style:{fontWeight:"bold"},children:["\u20ac",(e.price*e.quantity).toFixed(2)]})]},t))),(0,Ae.jsx)("hr",{style:{margin:"1rem 0"}}),(0,Ae.jsxs)("div",{className:"flex flex-between mb-1",children:[(0,Ae.jsx)("span",{children:"Subtotale:"}),(0,Ae.jsxs)("span",{children:["\u20ac",n.subtotal.toFixed(2)]})]}),n.deliveryFee>0&&(0,Ae.jsxs)("div",{className:"flex flex-between mb-1",children:[(0,Ae.jsx)("span",{children:"Consegna:"}),(0,Ae.jsxs)("span",{children:["\u20ac",n.deliveryFee.toFixed(2)]})]}),(0,Ae.jsxs)("div",{className:"flex flex-between",style:{fontSize:"1.1rem",fontWeight:"bold"},children:[(0,Ae.jsx)("span",{children:"Totale:"}),(0,Ae.jsxs)("span",{style:{color:"var(--secondary-color)"},children:["\u20ac",n.total.toFixed(2)]})]}),(0,Ae.jsxs)("div",{className:"mt-2",style:{fontSize:"0.9rem",color:"var(--text-light)"},children:[(0,Ae.jsx)("strong",{children:"Pagamento:"})," ","cash"===n.paymentMethod?"Contanti":"Carta"]})]})})]}),(0,Ae.jsx)("div",{className:"card mt-3",children:(0,Ae.jsxs)("div",{className:"card-body text-center",children:[(0,Ae.jsxs)("h4",{className:"mb-3",style:{color:"var(--primary-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-phone"})," Hai bisogno di aiuto?"]}),(0,Ae.jsx)("p",{style:{marginBottom:"1rem",color:"var(--text-light)"},children:"Per qualsiasi problema o informazione sul tuo ordine, contattaci"}),(0,Ae.jsxs)("a",{href:"tel:0399240339",className:"btn btn-primary",style:{marginRight:"1rem"},children:[(0,Ae.jsx)("i",{className:"fas fa-phone"})," Chiama: 039 9240339"]}),(0,Ae.jsxs)("a",{href:"https://wa.me/390399240339",className:"btn btn-success",target:"_blank",rel:"noopener noreferrer",children:[(0,Ae.jsx)("i",{className:"fab fa-whatsapp"})," WhatsApp"]})]})})]})}return(0,Ae.jsxs)("div",{className:"container fade-in",children:[(0,Ae.jsxs)("h1",{className:"text-center mb-3",style:{color:"var(--primary-color)"},children:[(0,Ae.jsx)("i",{className:"fas fa-list"})," I Tuoi Ordini"]}),(0,Ae.jsx)("p",{className:"text-center mb-3",style:{color:"var(--text-light)"},children:"Clicca su un ordine per vedere i dettagli e il tracking"}),(0,Ae.jsx)("div",{className:"grid grid-1 gap-3",children:t.slice().reverse().map((e=>{const t=l(e);return(0,Ae.jsx)("div",{className:"card cursor-pointer hover-shadow",onClick:()=>a(e),style:{transition:"all 0.2s"},children:(0,Ae.jsxs)("div",{className:"card-body",children:[(0,Ae.jsxs)("div",{className:"flex flex-between align-center",children:[(0,Ae.jsxs)("div",{className:"flex-1",children:[(0,Ae.jsxs)("div",{className:"flex align-center gap-2 mb-2",children:[(0,Ae.jsxs)("h3",{style:{margin:0,color:"var(--primary-color)"},children:["Ordine #",e.id.slice(-6)]}),(0,Ae.jsxs)("span",{className:"status-badge",style:{background:t.color,color:"white"},children:[(0,Ae.jsx)("i",{className:t.icon})," ",t.label]})]}),(0,Ae.jsxs)("div",{className:"flex gap-3 mb-2",style:{fontSize:"0.9rem",color:"var(--text-light)"},children:[(0,Ae.jsxs)("span",{children:[(0,Ae.jsx)("i",{className:"fas fa-calendar"})," ",s(e.createdAt)]}),(0,Ae.jsxs)("span",{children:[(0,Ae.jsx)("i",{className:"delivery"===e.orderType?"fas fa-truck":"fas fa-store"}),"delivery"===e.orderType?"Consegna":"Ritiro"]}),(0,Ae.jsxs)("span",{children:[(0,Ae.jsx)("i",{className:"fas fa-euro-sign"})," \u20ac",e.total.toFixed(2)]})]}),(0,Ae.jsxs)("div",{style:{fontSize:"0.9rem",color:"var(--text-light)"},children:[e.items.length," ",1===e.items.length?"articolo":"articoli"," \u2022",e.customer.name,"delivery"===e.orderType&&" \u2022 ".concat(e.customer.city)]})]}),(0,Ae.jsxs)("div",{className:"text-right",children:[(0,Ae.jsx)("div",{style:{fontSize:"1.1rem",fontWeight:"bold",color:t.color},children:o(e)}),(0,Ae.jsxs)("div",{style:{fontSize:"0.8rem",color:"var(--text-light)"},children:["Stimato: ",i(e)]}),(0,Ae.jsx)("i",{className:"fas fa-chevron-right",style:{color:"var(--text-light)",marginTop:"0.5rem"}})]})]}),(0,Ae.jsx)("div",{style:{width:"100%",height:"4px",background:"var(--background-light)",borderRadius:"2px",overflow:"hidden",marginTop:"1rem"},children:(0,Ae.jsx)("div",{style:{width:"".concat(t.progress,"%"),height:"100%",background:t.color,transition:"width 0.3s ease"}})})]})},e.id)}))}),(0,Ae.jsx)("div",{className:"text-center mt-3",children:(0,Ae.jsxs)("a",{href:"/menu",className:"btn btn-primary",children:[(0,Ae.jsx)("i",{className:"fas fa-plus"})," Nuovo Ordine"]})})]})};if("undefined"!==typeof document){const e=document.createElement("style");e.textContent="\n.timeline {\n  position: relative;\n  padding-left: 2rem;\n}\n\n.timeline::before {\n  content: '';\n  position: absolute;\n  left: 1rem;\n  top: 0;\n  bottom: 0;\n  width: 2px;\n  background: var(--border-color);\n}\n\n.timeline-item {\n  position: relative;\n  margin-bottom: 2rem;\n}\n\n.timeline-item.completed .timeline-icon {\n  background: var(--success-color);\n  color: white;\n}\n\n.timeline-item.active .timeline-icon {\n  background: var(--primary-color);\n  color: white;\n  animation: pulse 2s infinite;\n}\n\n.timeline-item.pending .timeline-icon {\n  background: var(--background-light);\n  color: var(--text-light);\n}\n\n.timeline-icon {\n  position: absolute;\n  left: -2rem;\n  top: 0;\n  width: 2rem;\n  height: 2rem;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.8rem;\n  border: 2px solid white;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.timeline-content h4 {\n  margin: 0 0 0.5rem 0;\n  color: var(--text-color);\n}\n\n.timeline-content p {\n  margin: 0 0 0.25rem 0;\n  color: var(--text-light);\n  font-size: 0.9rem;\n}\n\n.timeline-content small {\n  color: var(--text-light);\n  font-size: 0.8rem;\n}\n\n@keyframes pulse {\n  0% { box-shadow: 0 0 0 0 var(--primary-color); }\n  70% { box-shadow: 0 0 0 10px transparent; }\n  100% { box-shadow: 0 0 0 0 transparent; }\n}\n\n.cursor-pointer {\n  cursor: pointer;\n}\n\n.hover-shadow:hover {\n  box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n  transform: translateY(-2px);\n}\n\n.sticky-top {\n  position: sticky;\n  top: 1rem;\n}\n",document.head.appendChild(e)}const Ke=function(e){let{onLogin:t}=e;const[n,a]=(0,r.useState)(""),[l,i]=(0,r.useState)(""),[o,s]=(0,r.useState)(""),[c,u]=(0,r.useState)(!1);return(0,Ae.jsx)("div",{className:"login-container",children:(0,Ae.jsxs)("div",{className:"login-box",children:[(0,Ae.jsxs)("div",{className:"login-header",children:[(0,Ae.jsx)("h2",{children:"\ud83c\udf55 Dashboard Cucina"}),(0,Ae.jsx)("p",{children:"Accesso riservato al personale"})]}),(0,Ae.jsxs)("form",{onSubmit:e=>{e.preventDefault(),u(!0),s(""),setTimeout((()=>{"admin"===n&&"admin"===l?t(!0):(s("Username o password non corretti"),a(""),i("")),u(!1)}),500)},className:"login-form",children:[(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{htmlFor:"username",children:"Username"}),(0,Ae.jsx)("input",{type:"text",id:"username",value:n,onChange:e=>a(e.target.value),placeholder:"Inserisci username",required:!0,disabled:c})]}),(0,Ae.jsxs)("div",{className:"form-group",children:[(0,Ae.jsx)("label",{htmlFor:"password",children:"Password"}),(0,Ae.jsx)("input",{type:"password",id:"password",value:l,onChange:e=>i(e.target.value),placeholder:"Inserisci password",required:!0,disabled:c})]}),o&&(0,Ae.jsxs)("div",{className:"error-message",children:["\u26a0\ufe0f ",o]}),(0,Ae.jsx)("button",{type:"submit",className:"login-button",disabled:c,children:c?(0,Ae.jsxs)(Ae.Fragment,{children:[(0,Ae.jsx)("span",{className:"loading-spinner"}),"Accesso in corso..."]}):"Accedi alla Dashboard"})]}),(0,Ae.jsxs)("div",{className:"login-footer",children:[(0,Ae.jsx)("p",{children:"\ud83d\udd12 Area riservata"}),(0,Ae.jsx)("small",{children:"Contatta l'amministratore per problemi di accesso"})]})]})})};const Je=function(){const[e,t]=(0,r.useState)([]),[n,a]=(0,r.useState)([]),[l,i]=(0,r.useState)("customer"),[o,s]=(0,r.useState)(!1),[c,d]=(0,r.useState)([]);(0,r.useEffect)((()=>{const e=localStorage.getItem("pizzeria-cart"),n=localStorage.getItem("pizzeria-orders"),r=localStorage.getItem("pizzeria-auth"),l=localStorage.getItem("pizzeria-riders");if(e&&t(JSON.parse(e)),n&&a(JSON.parse(n)),r&&s(JSON.parse(r)),l)d(JSON.parse(l));else{const e=[{id:1,name:"Marco Bianchi",phone:"************",status:"available",currentOrder:null},{id:2,name:"Luca Rossi",phone:"************",status:"available",currentOrder:null}];d(e),localStorage.setItem("pizzeria-riders",JSON.stringify(e))}}),[]),(0,r.useEffect)((()=>{localStorage.setItem("pizzeria-cart",JSON.stringify(e))}),[e]),(0,r.useEffect)((()=>{localStorage.setItem("pizzeria-orders",JSON.stringify(n))}),[n]),(0,r.useEffect)((()=>{localStorage.setItem("pizzeria-auth",JSON.stringify(o))}),[o]),(0,r.useEffect)((()=>{localStorage.setItem("pizzeria-riders",JSON.stringify(c))}),[c]);const f=n=>{t(e.filter((e=>e.cartId!==n)))},m=()=>{t([])},p=async(e,t)=>{try{const r=n.find((t=>t.id===e));if(!r)return void console.error("Ordine non trovato:",e);if(r.backendOrderId){if(!(await fetch("".concat(Me("ORDERS").replace("/api/orders",""),"/api/orders/").concat(r.backendOrderId,"/status"),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:t})})).ok)throw new Error("Errore aggiornamento stato nel backend")}a(n.map((n=>n.id===e?u(u({},n),{},{status:t}):n))),console.log("Stato ordine ".concat(e," aggiornato a: ").concat(t))}catch(r){console.error("Errore aggiornamento stato ordine:",r),alert("Errore durante l'aggiornamento dello stato dell'ordine. Riprova.")}},h=(e,t)=>{a(n.map((n=>n.id===e?u(u({},n),{},{riderId:t,status:"out_for_delivery"}):n))),d(c.map((n=>n.id===t?u(u({},n),{},{status:"busy",currentOrder:e}):n)))},g=e=>{const t=n.find((t=>t.id===e));t&&t.riderId&&(d(c.map((e=>e.id===t.riderId?u(u({},e),{},{status:"available",currentOrder:null}):e))),p(e,"completed"))},v=e=>{s(e)},y=()=>{s(!1),i("customer")};return"dashboard"===l?(0,Ae.jsx)("div",{className:"App",children:o?(0,Ae.jsx)(Qe,{orders:n,updateOrderStatus:p,riders:c,assignRiderToOrder:h,completeDelivery:g,onBackToCustomer:()=>i("customer"),onLogout:y}):(0,Ae.jsx)(Ke,{onLogin:v})}):(0,Ae.jsx)(Te,{children:(0,Ae.jsxs)("div",{className:"App",children:[(0,Ae.jsx)(Ue,{cartCount:e.reduce(((e,t)=>e+t.quantity),0),onDashboardClick:()=>i("dashboard")}),(0,Ae.jsxs)(we,{children:[(0,Ae.jsx)(Se,{path:"/",element:(0,Ae.jsx)(Be,{})}),(0,Ae.jsx)(Se,{path:"/menu",element:(0,Ae.jsx)(Ve,{addToCart:n=>{const r=e.find((e=>e.id===n.id&&JSON.stringify(e.customizations)===JSON.stringify(n.customizations)));t(r?e.map((e=>e.cartId===r.cartId?u(u({},e),{},{quantity:e.quantity+1}):e)):[...e,u(u({},n),{},{quantity:1,cartId:Date.now()+Math.random()})])}})}),(0,Ae.jsx)(Se,{path:"/cart",element:(0,Ae.jsx)($e,{cart:e,removeFromCart:f,updateQuantity:(n,r)=>{r<=0?f(n):t(e.map((e=>e.cartId===n?u(u({},e),{},{quantity:r}):e)))},clearCart:m})}),(0,Ae.jsx)(Se,{path:"/checkout",element:(0,Ae.jsx)(He,{cart:e,addOrder:e=>{const t=u(u({},e),{},{id:e.id||Date.now().toString(),createdAt:e.createdAt||(new Date).toISOString(),status:e.status||"new"});return a([...n,t]),t.id},clearCart:m})}),(0,Ae.jsx)(Se,{path:"/order-tracking",element:(0,Ae.jsx)(qe,{orders:n})})]})]})})};l.createRoot(document.getElementById("root")).render((0,Ae.jsx)(r.StrictMode,{children:(0,Ae.jsx)(Je,{})}))})();
//# sourceMappingURL=main.516f1375.js.map